from sending_mail import send_email
from flask import Flask, render_template, request, redirect, url_for, jsonify, session, g, flash, current_app # Add flash and current_app
from urllib.parse import urlparse, urljoin
import sqlite3
import os
import firebase_admin
from firebase_admin import credentials, auth
import firebase_admin._token_gen  # Add this import for ExpiredIdTokenError
from functools import wraps
from datetime import date, datetime # Add this import at the top if not already present
import time
from dotenv import load_dotenv
import uuid # Add uuid for generating unique poll IDs
from utils.db_utils import delete_brewlog_record, delete_brewlog_for_user
from utils.log_utils import setup_activity_logger # For activity logging
from flask_wtf.csrf import CSRFProtect, generate_csrf # Import CSRFProtect
from flask_wtf import FlaskForm # Import FlaskForm
from wtforms import SelectField # Import SelectField for dropdown
from itsdangerous import URLSafeSerializer  # NEW import
from utils.encryption_utils import encrypt_id, decrypt_id
from sending_mail import send_email as send_mail # Import the send_email function
from flask import send_from_directory
import csv
from utils.email_actions import generate_email_verification_token, generate_password_reset_token, verify_email_token, verify_password_reset_token

# Load environment variables from .env file
load_dotenv()

app = Flask(__name__)
# Load the secret key from an environment variable
app.secret_key = os.environ.get('FLASK_SECRET_KEY', 'a_default_secret_key_for_development') # Provide a default for development if the env var isn't set
csrf = CSRFProtect(app) # Initialize CSRF protection
# Create a serializer using the secret key and a fixed salt
serializer = URLSafeSerializer(app.secret_key, salt="roaster-salt")  # NEW

# Initialize Activity Logger
# The log path can be configured via an environment variable ACTIVITY_LOG_PATH
activity_log_file_path = os.environ.get('ACTIVITY_LOG_PATH', 'logs/activity.log')
activity_logger = setup_activity_logger(log_file_path=activity_log_file_path)

# Add security headers to improve application security
@app.after_request
def add_security_headers(response):
    """Add security headers to all responses"""
    # Phase 1: Basic security headers (minimal risk)

    # Prevent browsers from interpreting files as a different MIME type
    # This is very safe and rarely causes issues
    response.headers['X-Content-Type-Options'] = 'nosniff'

    # Safer referrer policy that doesn't break functionality
    # This controls how much referrer information is sent when navigating away from a page
    response.headers['Referrer-Policy'] = 'strict-origin-when-cross-origin'

    # Prevent your page from being displayed in an iframe on another domain
    # This prevents clickjacking attacks
    response.headers['X-Frame-Options'] = 'SAMEORIGIN'

    # Enable the browser's built-in XSS filter
    # This provides an additional layer of protection against cross-site scripting attacks
    response.headers['X-XSS-Protection'] = '1; mode=block'

    # Phase 2: Medium-risk headers

    # Content Security Policy in Enforcement mode
    # This enforces the policy and blocks any violations
    # Policy has been tested and adjusted based on application needs
    csp = "default-src 'self'; " + \
        "script-src 'self' 'unsafe-inline' https://www.gstatic.com https://*.firebaseio.com https://*.googleapis.com " + \
        "https://apis.google.com https://code.jquery.com https://*.jquery.com https://cdnjs.cloudflare.com https://cdn.tailwindcss.com https://static.cloudflareinsights.com " + \
        "https://cdn.jsdelivr.net https://stackpath.bootstrapcdn.com; " + \
        "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com https://cdnjs.cloudflare.com " + \
        "https://code.jquery.com https://*.jquery.com https://cdn.tailwindcss.com; " + \
        "img-src 'self' data: https:; " + \
        "connect-src 'self' https://*.firebaseio.com https://*.googleapis.com; " + \
        "font-src 'self' https://fonts.gstatic.com https://cdnjs.cloudflare.com; " + \
        "object-src 'none'; " + \
        "media-src 'self'; " + \
        "frame-src 'self' https://*.firebaseapp.com https://*.googleapis.com https://apis.google.com;"
    response.headers['Content-Security-Policy'] = csp

    # Restrict which browser features can be used
    # This prevents abuse of sensitive browser features
    response.headers['Permissions-Policy'] = 'camera=(), microphone=(), geolocation=()'

    # Cross-Origin-Opener-Policy to allow Firebase authentication popups
    # This allows popups from the same origin to communicate back to the parent window
    # Required for Firebase signInWithPopup() to work properly
    response.headers['Cross-Origin-Opener-Policy'] = 'same-origin-allow-popups'

    return response

# @app.after_request
# def log_request_activity(response):
#     """Log incoming request activity after the request has been processed."""
#     try:
#         user_id = 'anonymous'
#         if hasattr(g, 'user') and g.user and hasattr(g.user, 'uid'):
#             user_id = g.user.uid
#         elif 'uid' in session:
#             user_id = session['uid']
#
#         log_data = {
#             "user_id": user_id,
#             "action": f"visited {request.path}",
#             "method": request.method,
#             "path": request.path,
#             "status_code": response.status_code,
#             "ip_address": request.remote_addr,
#             "user_agent": request.user_agent.string if request.user_agent else "Unknown"
#         }
#         # The timestamp is added by the JsonFormatter in log_utils
#         activity_logger.info("Page visit", extra={'custom_data': log_data})
#     except Exception as e:
#         # Log errors related to activity logging to the standard Flask logger
#         app.logger.error(f"Error in activity logging: {e}", exc_info=True)
#     return response

# Initialize Firebase Admin SDK
# Try to load from environment variables first, fall back to file if not available
try:
    # Check if all required Firebase Admin SDK environment variables are set
    firebase_env_vars = {
        'type': os.environ.get('FIREBASE_ADMIN_TYPE'),
        'project_id': os.environ.get('FIREBASE_ADMIN_PROJECT_ID'),
        'private_key_id': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY_ID'),
        'private_key': os.environ.get('FIREBASE_ADMIN_PRIVATE_KEY', '').replace('\\n', '\n'),
        'client_email': os.environ.get('FIREBASE_ADMIN_CLIENT_EMAIL'),
        'client_id': os.environ.get('FIREBASE_ADMIN_CLIENT_ID'),
        'auth_uri': os.environ.get('FIREBASE_ADMIN_AUTH_URI'),
        'token_uri': os.environ.get('FIREBASE_ADMIN_TOKEN_URI'),
        'auth_provider_x509_cert_url': os.environ.get('FIREBASE_ADMIN_AUTH_PROVIDER_X509_CERT_URL'),
        'client_x509_cert_url': os.environ.get('FIREBASE_ADMIN_CLIENT_X509_CERT_URL')
    }

    # Check if all required variables are present
    if all(firebase_env_vars.values()):
        # Use environment variables
        cred = credentials.Certificate(firebase_env_vars)
    else:
        # Fall back to service account file
        service_account_path = os.environ.get('FIREBASE_SERVICE_ACCOUNT_PATH', 'instance/serviceAccountKey.json')
        cred = credentials.Certificate(service_account_path)

    firebase_admin.initialize_app(cred)
except Exception as e:
    app.logger.critical(f"CRITICAL: Error initializing Firebase Admin SDK: {e}", exc_info=True)
    # In production, you might want to handle this more gracefully
    # For now, we'll re-raise to prevent the app from starting with broken authentication
    raise

# Add before_request handler to load user info if session token exists
@app.before_request
def load_logged_in_user():
    g.user = None  # Initialize g.user
    g.is_admin = False # Initialize g.is_admin

    if 'id_token' in session:
        try:
            token_details = auth.verify_id_token(session['id_token'], clock_skew_seconds=60)
            g.user = auth.get_user(token_details['uid'])

            if g.user:
                # Check if user is admin
                admin_uid_env = os.environ.get('ADMIN_UIDS')
                if admin_uid_env:
                    admin_uids = [uid.strip() for uid in admin_uid_env.split(',')]
                    if g.user.uid in admin_uids:
                        g.is_admin = True
                # No else needed for g.is_admin as it's initialized to False

                # Check if user is a roaster
                conn = get_db_connection()
                cursor = conn.cursor()
                cursor.execute("SELECT roaster_id FROM roasters WHERE uid = ?", (g.user.uid,))
                roaster_record = cursor.fetchone()
                conn.close()

                if roaster_record:
                    session['roaster_user'] = True
                    session['roaster_id'] = roaster_record['roaster_id']
                else:
                    session['roaster_user'] = False
                    session.pop('roaster_id', None)
        except firebase_admin._token_gen.ExpiredIdTokenError as e:
            app.logger.warning(f"Expired ID token: {e}")
            g.user = None
            g.is_admin = False
            session.clear()
            # Optionally, you can flash a message here if you want to notify the user
        except Exception as e:
            app.logger.error(f"Error loading user: {e}", exc_info=True)
            g.user = None
            g.is_admin = False
            session['roaster_user'] = False
            session.pop('roaster_id', None)
    else:
        # Ensure g.user and g.is_admin are reset if no id_token
        g.user = None
        g.is_admin = False
        session['roaster_user'] = False
        session.pop('roaster_id', None)

# Helper function to check for safe URLs (should be at module level)
def is_safe_url(target):
    ref_url = urlparse(request.host_url)
    test_url = urlparse(urljoin(request.host_url, target))
    return test_url.scheme in ('http', 'https') and \
           ref_url.netloc == test_url.netloc

DATABASE = 'coffee_database.db' # Should be at module level
GENERAL_DATABASE = 'general.db' # Path to the general database

def get_general_db_connection():
    """Establishes a connection to the general.db SQLite database."""
    conn = sqlite3.connect(GENERAL_DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

# Decorator for login required
def login_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        if 'id_token' not in session:
            app.logger.warning("No id_token in session, redirecting to home")
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('home'))

        try:
            # Verify token and load full user record
            token_details = auth.verify_id_token(session['id_token'], check_revoked=True, clock_skew_seconds=60)
            g.user = auth.get_user(token_details['uid'])

            # ---> START EMAIL VERIFICATION CHECK
            if not g.user.email_verified:
                # Allow access only to the verification notice page or logout
                allowed_endpoints = ['verify_email_notice', 'logout', 'static', 'get_firebase_config', 'check_session']
                if request.endpoint not in allowed_endpoints:
                    flash("Please verify your email address before proceeding.", "info")
                    return redirect(url_for('verify_email_notice'))
            # ---> END EMAIL VERIFICATION CHECK

        except auth.RevokedIdTokenError:
            flash("Your session has been revoked. Please log in again.", "warning")
            session.clear()
            return redirect(url_for('home'))
        except auth.UserDisabledError:
            flash("Your account has been disabled.", "danger")
            session.clear()
            return redirect(url_for('home'))
        except Exception as e:
            app.logger.error(f"Error verifying token or checking email verification: {e}", exc_info=True) # Changed to app.logger.error
            flash("Your session is invalid or expired. Please log in again.", "warning")
            session.clear()
            return redirect(url_for('home'))

        return f(*args, **kwargs)
    return decorated_function

# Decorator for admin required
def admin_required(f):
    @wraps(f)
    def decorated_function(*args, **kwargs):
        # First, ensure user is logged in
        if not g.user:
            flash("Please log in to access this page.", "warning")
            return redirect(url_for('login', next=request.url))

        admin_uid_env = os.environ.get('ADMIN_UIDS')
        if not admin_uid_env:
            app.logger.error("@admin_required: ADMIN_UIDS environment variable is not set.")
            flash("Administrative access is misconfigured.", "danger")
            return redirect(url_for('home'))

        admin_uids = [uid.strip() for uid in admin_uid_env.split(',')]

        if g.user.uid not in admin_uids:
            flash("You do not have permission to access this page.", "danger")
            return redirect(url_for('home'))

        return f(*args, **kwargs)
    return decorated_function

@app.route('/login')
def login():
    # Check if the request is coming from our site
    referrer = request.referrer

    # If no referrer or not from our site, redirect to home
    if not referrer or not referrer.startswith(request.host_url):
        return redirect(url_for('home'))

    # Check if referrer is from an allowed page (index or other main pages)
    allowed_paths = ['/', '/index', '/index.html', '/search_beans', '/show_brew']
    referrer_path = referrer.replace(request.host_url, '').split('?')[0].rstrip('/')

    if referrer_path not in allowed_paths and not referrer_path.startswith('search_beans'):
        return redirect(url_for('home'))

    next_page = request.args.get('next')
    if next_page and not is_safe_url(next_page):
        app.logger.warning(f"Unsafe next_page URL detected and discarded: {next_page}")
        next_page = None # Discard unsafe URL
    if not next_page: # Default to home if no next_page or if it was unsafe
        next_page = url_for('home')

    return render_template('login.html', next=next_page)

@app.route('/register')
def register():
    # Check if the request is coming from our site
    referrer = request.referrer

    # If no referrer or not from our site, redirect to home
    if not referrer or not referrer.startswith(request.host_url):
        return redirect(url_for('home'))

    # Allow access from any page on our site
    # The referrer check above already ensures it's from our domain
    return render_template('register.html')

@app.route('/login_modal')
@app.route('/login_modal.html')
def login_modal():
    # Prevent direct access to login_modal.html
    # Always redirect to home page
    return redirect(url_for('home'))

@app.route('/set_token', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection since this is called during registration
def set_token():
    try:
        data = request.get_json()
        if not data:
            return jsonify({'message': 'No JSON data provided'}), 400

        token = data.get('token')
        allow_unverified = data.get('allow_unverified', False)

        if not token:
            return jsonify({'message': 'No token provided'}), 400

        # Verify the token
        decoded_token = auth.verify_id_token(token, clock_skew_seconds=60)
        user = auth.get_user(decoded_token['uid'])

        # Check email verification unless explicitly allowed (for registration flow)
        if not user.email_verified and not allow_unverified:
            return jsonify({'message': 'Email not verified. Please check your inbox and verify your email before logging in.'}), 401

        # Set session data
        session['id_token'] = token
        session['uid'] = decoded_token['uid']
        
        # Log user login activity
        log_data = {
            "user_id": decoded_token['uid']
        }
        activity_logger.info("User login", extra={'custom_data': log_data})

        return jsonify({'message': 'Token set successfully'}), 200

    except auth.InvalidIdTokenError:
        return jsonify({'message': 'Invalid Firebase token'}), 401
    except auth.ExpiredIdTokenError:
        return jsonify({'message': 'Expired Firebase token'}), 401
    except Exception as e:
        app.logger.error(f"Error in set_token: {e}")
        return jsonify({'message': 'Server error'}), 500

@app.route('/reset_password', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection for password reset requests
def reset_password():
    """Send a password reset email to the user via backend"""
    data = request.get_json()
    email = data.get('email')

    if not email:
        return jsonify({'message': 'No email provided'}), 400

    try:
        # Check if the user exists first
        try:
            user = auth.get_user_by_email(email)
            app.logger.info(f"Sending password reset email to existing user: {email}")

            # Generate password reset token
            token = generate_password_reset_token(user.email, user.uid)

            # Create reset URL
            reset_url = url_for('reset_password_form', token=token, _external=True)

            # Create email content
            from utils.email_actions import create_password_reset_email_html
            email_html = create_password_reset_email_html(reset_url, user.email)

            # Send email
            email_sent = send_email(
                recipient=user.email,
                subject="Israeli Coffee - Password Reset Request",
                message=email_html
            )

            if email_sent:
                app.logger.info(f"Password reset email sent successfully to {email}")
            else:
                app.logger.error(f"Failed to send password reset email to {email}")

        except auth.UserNotFoundError:
            # User doesn't exist, but we'll still return success to prevent email enumeration
            app.logger.info(f"Password reset requested for non-existent email: {email}")

        # Always return success to prevent email enumeration
        return jsonify({'message': 'אם האימייל רשום במערכת, נשלח אליך קישור לאיפוס סיסמה'}), 200

    except Exception as e:
        app.logger.error(f"Error in password reset process: {e}", exc_info=True)
        # Return a generic message to avoid revealing if the email exists
        return jsonify({'message': 'אם האימייל רשום במערכת, נשלח אליך קישור לאיפוס סיסמה'}), 200

@app.route('/logout')
def logout():
    try:
        user_id = session.get('uid', 'anonymous')
        if user_id != 'anonymous':
            log_data = {
                "user_id": user_id
            }
            # The JsonFormatter will add timestamp and level.
            activity_logger.info("User logout", extra={'custom_data': log_data})
    except Exception as e:
        app.logger.error(f"Error during logout activity logging: {e}", exc_info=True)
    finally:
        session.clear()  # Clear all session data
    return redirect(url_for('home'))

def get_db_connection():
    conn = sqlite3.connect(DATABASE)
    conn.row_factory = sqlite3.Row
    return conn

@app.route('/', defaults={'path': ''})
@app.route('/<path:path>')
def home(path):
    # Check for the special bypass path
    bypass_path = 'kdjHJFUIKJ-KJkdjKJKFweE'  # Do not use # in URLs
    # Remove any trailing slashes for comparison
    normalized_path = path.rstrip('/')
    # Check WebpageON environment variable
    webpage_on = os.environ.get('WebpageON', '1')
    # If user is logged in, always show index.html
    if getattr(g, 'user', None):
        return render_template('index.html')
    # If WebpageON is 1 or the special bypass path is used, show index.html
    if webpage_on == '1' or normalized_path == bypass_path:
        return render_template('index.html')
    # Otherwise, show under construction page
    return render_template('index_construction.html')

@app.route('/add_roaster', methods=['GET', 'POST'])
@admin_required
def add_roaster():
    if request.method == 'POST':
        # Admin is adding a roaster. Roaster is not linked to a UID at this stage.
        # UID will be linked via /link_roaster by an admin.
        name = request.form['name']
        address = request.form['address']
        city = request.form['city']
        zip_code = request.form['zip'] # Renamed to avoid conflict with zip()
        email = request.form['email']
        webpage = request.form['webpage']
        minimun_shipping = request.form['minimun_shipping']
        shipping_cost = request.form['shipping_cost']

        # TODO: Add comprehensive input validation here for all fields

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            # Insert roaster without UID. UID will be added via /link_roaster.
            # Ensure your 'roasters' table schema allows 'uid' to be NULL or has a default.
            cursor.execute('''
                INSERT INTO roasters (name, address, city, zip, email, webpage, minimun_shipping, shipping_cost)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (name, address, city, zip_code, email, webpage, minimun_shipping, shipping_cost))
            new_roaster_id = cursor.lastrowid
            conn.commit()
            flash(f"Roaster '{name}' created successfully with ID {new_roaster_id}. You can now link it to a user.", "success")
            return redirect(url_for('link_roaster')) # Redirect admin to link this new roaster
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Admin database error adding roaster: {e}")
            flash("Failed to create roaster profile due to a database error.", "danger")
        finally:
            if conn:
                conn.close()
        # If error, fall through to render the form again, or redirect to an admin dashboard
        return redirect(url_for('add_roaster')) # Or an admin dashboard

    return render_template('add_roaster.html')
@app.route('/deals', methods=['GET'])
@login_required
def show_deals():
    conn = get_db_connection()
    deals = []
    try:
        # Join deals with roasters to get roaster names
        cursor = conn.execute('''
            SELECT
                d.deal_id,
                d.deal_text,
                d.deal_code,
                d.deal_date,
                r.name AS roaster_name,
                r.webpage AS roaster_webpage
            FROM deals d
            JOIN roasters r ON d.roaster_id = r.roaster_id
            ORDER BY d.deal_date DESC
        ''')
        deals_data = cursor.fetchall()
        deals = []
        for deal in deals_data:
            deal_dict = dict(deal)
            if deal_dict['deal_date']:
                try:
                    deal_dict['deal_date'] = datetime.strptime(deal_dict['deal_date'], '%Y-%m-%d').date()
                except (ValueError, TypeError):
                    # Keep original value if parsing fails
                    pass
            deals.append(deal_dict)
    except sqlite3.Error as e:
        app.logger.error(f"Database error fetching deals: {e}", exc_info=True)
        flash("Failed to load deals due to a database error.", "danger")
    finally:
        conn.close()
    return render_template('deals.html', deals=deals, is_admin=g.is_admin)

@app.route('/deal/delete/<int:deal_id>', methods=['POST'])
@admin_required
def delete_deal(deal_id):
    conn = get_db_connection()
    try:
        cursor = conn.cursor()
        cursor.execute("DELETE FROM deals WHERE deal_id = ?", (deal_id,))
        conn.commit()
        if cursor.rowcount > 0:
            # Use JavaScript alert by injecting a script into the response
            return f"<script>alert('ההצעה נמחקה בהצלחה.'); window.location.href='{url_for('show_deals')}';</script>"
            activity_logger.info(f"Admin {g.user.uid} deleted deal_id: {deal_id}", extra={'custom_data': {'deal_id': deal_id, 'admin_uid': g.user.uid}})
        else:
            flash("הצעה לא נמצאה או לא נמחקה.", "warning")
            activity_logger.warning(f"Admin {g.user.uid} attempted to delete non-existent deal_id: {deal_id}", extra={'custom_data': {'deal_id': deal_id, 'admin_uid': g.user.uid}})
    except sqlite3.Error as e:
        conn.rollback()
        app.logger.error(f"Database error deleting deal {deal_id}: {e}", exc_info=True)
        flash("שגיאה במחיקת ההצעה.", "danger")
    finally:
        conn.close()
    return redirect(url_for('show_deals'))
@app.route('/add_deal', methods=['GET', 'POST'])
@admin_required
def add_deal():
    conn = get_db_connection()
    cursor = conn.cursor()
    roasters = []
    try:
        cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
        roasters = cursor.fetchall()
    except sqlite3.Error as e:
        app.logger.error(f"Database error fetching roasters for add_deal: {e}")
        flash("Failed to load roasters due to a database error.", "danger")
        conn.close()
        return redirect(url_for('home')) # Redirect to home or an error page

    if request.method == 'POST':
        roaster_id = request.form.get('roaster_id')
        deal_text = request.form.get('deal_text').replace('\r\n', '<br>').replace('\n', '<br>')
        coupon_code = request.form.get('coupon_code')

        # Basic validation
        if not all([roaster_id, deal_text]):
            flash("בית קליה ושדה המבצע הם שדות חובה.", "danger") # Roaster and Deal text are required fields.
            conn.close()
            return render_template('add_deal.html', roasters=roasters)

        try:
            # Insert new deal into 'deals' table
            cursor.execute('''
                INSERT INTO deals (roaster_id, deal_text, deal_code)
                VALUES (?, ?, ?)
            ''', (roaster_id, deal_text, coupon_code))
            conn.commit()
            flash("המבצע נוסף בהצלחה!", "success") # Deal added successfully!
            return redirect(url_for('home')) # Redirect to home or a confirmation page
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error adding deal: {e}")
            flash("שגיאה בהוספת המבצע למסד הנתונים.", "danger") # Error adding deal to the database.
        finally:
            if conn:
                conn.close()
    
    conn.close() # Close connection for GET request
    return render_template('add_deal.html', roasters=roasters)

@admin_required
def add_deal():
    conn = get_db_connection()
    cursor = conn.cursor()
    roasters = []
    try:
        cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
        roasters = cursor.fetchall()
    except sqlite3.Error as e:
        app.logger.error(f"Database error fetching roasters for add_deal: {e}")
        flash("Failed to load roasters due to a database error.", "danger")
        conn.close()
        return redirect(url_for('home')) # Redirect to home or an error page

    if request.method == 'POST':
        roaster_id = request.form.get('roaster_id')
        deal_text = request.form.get('deal_text')
        coupon_code = request.form.get('coupon_code')

        # Basic validation
        if not all([roaster_id, deal_text]):
            flash("בית קליה ושדה המבצע הם שדות חובה.", "danger") # Roaster and Deal text are required fields.
            conn.close()
            return render_template('add_deal.html', roasters=roasters)

        try:
            # Insert new deal into 'deals' table
            cursor.execute('''
                INSERT INTO deals (roaster_id, deal_text, coupon_code, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (roaster_id, deal_text, coupon_code))
            conn.commit()
            flash("המבצע נוסף בהצלחה!", "success") # Deal added successfully!
            return redirect(url_for('home')) # Redirect to home or a confirmation page
        except sqlite3.Error as e:
            conn.rollback()
@admin_required
def add_deal():
    conn = get_db_connection()
    cursor = conn.cursor()
    roasters = []
    try:
        cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
        roasters = cursor.fetchall()
    except sqlite3.Error as e:
        app.logger.error(f"Database error fetching roasters for add_deal: {e}")
        flash("Failed to load roasters due to a database error.", "danger")
        conn.close()
        return redirect(url_for('home')) # Redirect to home or an error page

    if request.method == 'POST':
        roaster_id = request.form.get('roaster_id')
        deal_text = request.form.get('deal_text')
        coupon_code = request.form.get('coupon_code')

        # Basic validation
        if not all([roaster_id, deal_text]):
            flash("בית קליה ושדה המבצע הם שדות חובה.", "danger") # Roaster and Deal text are required fields.
            conn.close()
            return render_template('add_deal.html', roasters=roasters)

        try:
            # Insert new deal into 'deals' table
            cursor.execute('''
                INSERT INTO deals (roaster_id, deal_text, coupon_code, created_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            ''', (roaster_id, deal_text, coupon_code))
            conn.commit()
            flash("המבצע נוסף בהצלחה!", "success") # Deal added successfully!
            return redirect(url_for('home')) # Redirect to home or a confirmation page
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error adding deal: {e}")
            flash("שגיאה בהוספת המבצע למסד הנתונים.", "danger") # Error adding deal to the database.
        finally:
            if conn:
                conn.close()
    
    conn.close() # Close connection for GET request
    return render_template('add_deal.html', roasters=roasters)

@app.route('/add_bean', methods=['GET', 'POST'])
@login_required
def add_bean():
    # Use roaster_id from session if available
    roaster_id = session.get('current_roaster_id', '')
    is_roaster_user = False
    if roaster_id and session.get('roaster_user') and str(session.get('roaster_id')) == str(roaster_id):
        is_roaster_user = True
    elif session.get('roaster_user') and session.get('roaster_id'):
        roaster_id = session.get('roaster_id')
        is_roaster_user = True

    conn = get_db_connection()
    cursor = conn.cursor()

    roaster_name = None
    if roaster_id:
        try:
            cursor.execute("SELECT name FROM roasters WHERE roaster_id = ?", (roaster_id,))
            result = cursor.fetchone()
            if result:
                roaster_name = result[0]
        except sqlite3.Error as e:
            app.logger.error(f"Database error fetching roaster name: {e}")
            flash("Failed to load roaster data due to a database error.", "danger")
            conn.close()
            return redirect(url_for('home'))
    else:
        flash("Roaster ID not found in session. Please log in as a roaster.", "danger")
        conn.close()
        return redirect(url_for('login'))

    # Load accessible roasters - if user is a roaster, only show their roaster
    if is_roaster_user:
        cursor.execute("SELECT roaster_id, name from roasters WHERE roaster_id = ?", (roaster_id,))
    else:
        cursor.execute("SELECT roaster_id, name from roasters")

    roasters = cursor.fetchall()
    cursor.execute("SELECT country FROM countries ORDER BY country ASC")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor ASC")
    flavors = cursor.fetchall()
    conn.close()

    # Convert roaster_id to integer if present
    try:
        if roaster_id:
            roaster_id = int(roaster_id)
    except ValueError:
        roaster_id = ''

    default_arabica = request.args.get('arabica') or session.get('default_arabica', '100')
    default_weight = request.args.get('weight') or session.get('default_weight', '1000')
    default_mix = request.args.get('mix') or session.get('default_mix', 'True')

    if request.method == 'GET':
        session['default_arabica'] = default_arabica
        session['default_weight'] = default_weight
        session['default_mix'] = default_mix

    if request.method == 'POST':
        name = request.form['name']
        roast_level = request.form['roast_level']
        origin = request.form['origin']
        flavors = request.form['flavors']
        price = request.form['price']
        currency = request.form['currency']
        weight_grams = request.form['weight_grams']
        link = request.form['link']
        image_url = request.form['image_url']
        description = request.form['description']
        
        # Input validation (basic example)
        if not all([name, roast_level, origin, price, currency, weight_grams, link]):
            flash("Please fill in all required fields.", "danger")
            conn.close()
            return render_template('add_bean.html', roaster_name=roaster_name, roaster_id=roaster_id, is_roaster_user=is_roaster_user, roaster_user=session.get('roaster_user'))

        try:
            cursor.execute('''
                INSERT INTO beans (roaster_id, name, roast_level, origin, flavors, price, currency, weight_grams, link, image_url, description)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (roaster_id, name, roast_level, origin, flavors, price, currency, weight_grams, link, image_url, description))
            conn.commit()
            flash(f"Bean '{name}' added successfully for {roaster_name}!", "success")
            return redirect(url_for('manage_roaster')) # Redirect back to manage roaster page
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error adding bean: {e}")
            flash("Failed to add bean due to a database error.", "danger")
        finally:
            if conn:
                conn.close()
    
    return render_template(
        'add_bean.html',
        roaster_name=roaster_name,
        roaster_id=roaster_id,
        is_roaster_user=is_roaster_user,
        roaster_user=session.get('roaster_user')
    )

@app.route('/add_country', methods=['POST'])
@login_required
def add_country():
    data = request.get_json()
    country = data['country']
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO countries (country) VALUES (?)', (country,))
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/add_flavor', methods=['POST'])
@login_required
def add_flavor():
    data = request.get_json()
    flavor = data['flavor']
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute('INSERT INTO coffee_flavors (c_flavor) VALUES (?)', (flavor,))
    conn.commit()
    conn.close()
    return jsonify({'success': True})

@app.route('/get_roasters_by_city', methods=['POST'])
def get_roasters_by_city():
    data = request.get_json()
    if not data or 'city' not in data:
        return jsonify({'error': 'City parameter is required'}), 400

    city = data['city']

    try:
        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute("SELECT roaster_id, name FROM roasters WHERE city = ?", (city,))
        roasters_data = cursor.fetchall()
        conn.close()

        roasters = [{'id': str(row['roaster_id']), 'name': row['name']} for row in roasters_data]
        return jsonify({'roasters': roasters})
    except Exception as e:
        if 'conn' in locals():
            conn.close()
        return jsonify({'error': str(e)}), 500

@app.route('/update_beans', methods=['GET', 'POST'])
@login_required
def update_beans():
    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT roaster_id, name FROM roasters")
    roasters = cursor.fetchall()
    roaster_id = request.args.get('roaster_id')

    beans = []
    if request.method == 'POST':
        # ...existing code...
        list_all = request.form.get('list_all')
        roaster_id_post = request.form.get('roaster_id')
        if list_all:
            cursor.execute("SELECT * FROM beans")
        elif roaster_id_post:
            cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id_post,))
        beans = cursor.fetchall()
    elif roaster_id:
        cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id,))
        beans = cursor.fetchall()

    conn.close()
    return render_template('update_beans.html', roasters=roasters, beans=beans)

@app.route('/edit_bean', methods=['POST', 'GET'])
@login_required
def edit_bean():
    if request.method == 'POST':
        bean_id = request.form.get('bean_id')
        if not bean_id:
            return "Missing bean_id", 400
        session['current_bean_id'] = bean_id
    else:
        bean_id = session.get('current_bean_id')
        if not bean_id:
            return redirect(url_for('manage_roaster'))

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT * FROM beans WHERE bean_id = ?", (bean_id,))
    bean = cursor.fetchone()

    if not bean:
        conn.close()
        flash("Bean not found.", "danger")
        return redirect(url_for('manage_roaster'))

    # Authorization Check: Ensure the bean belongs to the logged-in roaster
    authenticated_roaster_id = session.get('roaster_id')
    if not session.get('roaster_user') or str(bean['roaster_id']) != str(authenticated_roaster_id):
        conn.close()
        flash("You are not authorized to edit this bean.", "danger")
        return redirect(url_for('manage_roaster'))

    cursor.execute("SELECT country FROM countries ORDER BY country ASC")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor ASC")
    flavors = cursor.fetchall()
    # current_roaster_id is now confirmed to be the authenticated roaster's ID for this bean
    session['current_roaster_id'] = bean['roaster_id']
    conn.close()

    if request.method == 'POST' and 'bean_name' in request.form:
        # This is a form submission to update the bean
        bean_id = request.form.get('bean_id')
        if not bean_id:
            bean_id = session.get('current_bean_id')
            if not bean_id:
                return "Missing bean_id", 400

        bean_name = request.form['bean_name']
        origin = request.form['origin']
        processing = request.form['processing']
        elevation = request.form['elevation']
        flavors_input = request.form['flavors']
        acidity = request.form['acidity']
        after_taste = request.form['after_taste']
        roast_level = request.form['roast_level']
        body = request.form['body']
        arabica = request.form['arabica'] if request.form['arabica'] else 0
        robusta = request.form['robusta'] if request.form['robusta'] else 0
        mix = request.form['mix'] == 'True'
        price = request.form['price']
        weight = request.form['weight']
        image_file = request.form['image_file']
        turkish = request.form.get('turkish', 'False') == 'True'
        espresso = request.form.get('espresso', 'False') == 'True'
        french_press = request.form.get('french_press', 'False') == 'True'
        pour_over = request.form.get('pour_over', 'False') == 'True'
        drip = request.form.get('drip', 'False') == 'True'
        cold_brew = request.form.get('cold_brew', 'False') == 'True'
        SCA_score = request.form['SCA_score']
        Speciality = request.form.get('Speciality', 'False') == 'True'
        decaf = request.form.get('decaf', 'False') == 'True'

        # Ensure we use the authenticated roaster_id for the update operation
        auth_roaster_id_for_update = session.get('roaster_id')
        if not auth_roaster_id_for_update: # Should not happen due to @login_required and previous checks
            flash("Authentication error. Please log in again.", "danger")
            return redirect(url_for('manage_roaster'))

        conn = get_db_connection()
        cursor = conn.cursor()
        try:
            cursor.execute('''
                UPDATE beans SET
                    bean_name = ?, origin = ?, processing = ?, elevation = ?, flavors = ?, acidity = ?,
                    after_taste = ?, roast_level = ?, body = ?, arabica = ?, robusta = ?, mix = ?,
                    price = ?, weight = ?, image_file = ?, turkish = ?, espresso = ?, french_press = ?, pour_over = ?, drip = ?, cold_brew = ?, SCA_score = ?, Speciality = ?, decaf = ?
                WHERE bean_id = ? AND roaster_id = ?
            ''', (bean_name, origin, processing, elevation, flavors_input, acidity, after_taste, roast_level, body, arabica, robusta, mix,
                  price, weight, image_file, turkish, espresso, french_press, pour_over, drip, cold_brew, SCA_score, Speciality, decaf, bean_id, auth_roaster_id_for_update))

            if cursor.rowcount == 0:
                flash("Failed to update bean. It might have been deleted or you lack permission.", "warning")
            else:
                flash("Bean updated successfully.", "success")
            conn.commit()
        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error updating bean {bean_id} for roaster {auth_roaster_id_for_update}: {e}")
            flash("An error occurred while updating the bean.", "danger")
        finally:
            conn.close()
        return redirect(url_for('manage_roaster'))

    return render_template('edit_bean.html', bean=bean, countries=countries, flavors=flavors)

@app.route('/delete_bean', methods=['POST'])
@login_required
def delete_bean():
    bean_id = request.form.get('bean_id')
    if not bean_id:
        flash("Bean ID is missing.", "danger")
        return redirect(url_for('manage_roaster'))

    if not session.get('roaster_user'):
        flash("You must be a roaster to delete beans.", "danger")
        return redirect(url_for('home')) # Or perhaps 'manage_roaster' if appropriate

    roaster_id = session.get('roaster_id')
    if not roaster_id:
        flash("Roaster ID not found in your session.", "danger")
        return redirect(url_for('home')) # Or 'manage_roaster'

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        # Ensure the bean belongs to the authenticated roaster before deleting
        cursor.execute("DELETE FROM beans WHERE bean_id = ? AND roaster_id = ?", (bean_id, roaster_id))
        if cursor.rowcount == 0:
            flash("Bean not found or you do not have permission to delete it.", "warning")
        else:
            flash("Bean deleted successfully.", "success")
        conn.commit()
    except sqlite3.Error as e:
        conn.rollback()
        app.logger.error(f"Database error deleting bean {bean_id} for roaster {roaster_id}: {e}")
        flash("An error occurred while deleting the bean.", "danger")
    finally:
        conn.close()
    return redirect(url_for('manage_roaster'))

@app.route('/delete_brew/<int:brewlog_id>', methods=['POST'])
@login_required
def delete_brew(brewlog_id):  # Renamed from delete_bean to delete_brew to avoid conflict
    if not hasattr(g, 'user') or not g.user or not hasattr(g.user, 'uid'):
        flash("User not properly authenticated.", "danger")
        return redirect(url_for('show_brew'))

    success, message = delete_brewlog_for_user(uid=g.user.uid, record_id=brewlog_id)

    if success:
        # The message from delete_brewlog_for_user might be too technical (e.g., "Deleted 1 record(s)")
        # We can customize it here.
        flash("Brew log entry deleted successfully.", "success")
    else:
        # If message indicates "Record not found" or similar, it's implicitly handled by rowcount in delete_brewlog_for_user
        # For other errors, the message from delete_brewlog_for_user is "Database operation failed" or "An error occurred..."
        flash(message, "danger") # Or a more user-friendly generic message like "Failed to delete brew log entry."

    return redirect(url_for('show_brew'))

@app.route('/save_brew', methods=['POST'])
def save_brew():
    """
    Save a new brew record to the database.
    Requires user to be logged in and a valid bean_id to be provided.
    Returns error for unregistered users.
    """
    # Check if user is logged in - return error if not
    if not g.user:
        return jsonify({'success': False, 'error': 'You must be logged in to save brew records. Please register or log in.'}), 401

    from utils.error_handler import handle_validation_error, handle_database_error, handle_general_error

    conn = None
    try:
        data = request.get_json()
        uid = session.get('uid')

        # Validate required fields
        if not uid:
            return handle_validation_error(missing_fields=['User ID'])

        if not data or not isinstance(data, dict):
            return handle_validation_error(missing_fields=['Request data'])

        if 'bean_id' not in data:
            return handle_validation_error(missing_fields=['bean_id'])

        # Try to convert bean_id to integer to validate it
        try:
            bean_id = int(data['bean_id'])
        except (ValueError, TypeError):
            return handle_validation_error(invalid_fields=['bean_id'])

        # Build the SQL query dynamically based on provided fields
        fields = []
        values = []
        params = []

        # Always include uid and bean_id - ensure uid is stored as a string
        fields.extend(['uid', 'bean_id'])
        values.extend(['?', '?'])
        params.extend([str(uid), bean_id])  # Explicitly convert uid to string and use validated bean_id

        # Optional fields with validation
        optional_fields = [
            'bean_purchase_date', 'bean_roasting_date', 'brew_method',
            'grind_settings', 'brew_temp', 'PreInfusionTimeSec', 'brew_time',
            'coffee_dose', 'coffee_output', 'aroma', 'acidity', 'sweetness',
            'bitterness', 'body', 'aftertaste', 'overall_rating'
        ]

        # Numeric fields that should be validated
        numeric_fields = ['grind_settings', 'brew_temp', 'PreInfusionTimeSec', 'brew_time',
                         'coffee_dose', 'coffee_output', 'overall_rating']

        invalid_fields = []

        for field in optional_fields:
            if field in data and data[field] is not None:
                # Validate numeric fields
                if field in numeric_fields:
                    try:
                        # Convert to appropriate type
                        if field in ['PreInfusionTimeSec', 'brew_time', 'overall_rating']:
                            value = int(data[field])
                        else:
                            value = float(data[field])

                        fields.append(field)
                        values.append('?')
                        params.append(value)
                    except (ValueError, TypeError):
                        invalid_fields.append(field)
                else:
                    # For non-numeric fields, just add them
                    fields.append(field)
                    values.append('?')
                    params.append(data[field])

        # If we found invalid fields, return an error
        if invalid_fields:
            return handle_validation_error(invalid_fields=invalid_fields)

        # Add brew_date with today's date in d-m-yyyy format
        today = date.today().strftime('%d-%m-%Y')
        fields.append('brew_date')
        values.append('?')
        params.append(today)

        # Create a safe parameterized query
        # Create the SQL statement with placeholders
        placeholders = ', '.join(['?'] * len(fields))
        columns = ', '.join(fields)

        query = f"INSERT INTO brewlog ({columns}) VALUES ({placeholders})"

        try:
            conn = get_db_connection()
            cursor = conn.cursor()
            cursor.execute(query, params)
            conn.commit()
            conn.close()
            return jsonify({'success': True})
        except sqlite3.Error as db_error:
            return handle_database_error(db_error, operation="save_brew",
                                        context={"fields": fields})
    except Exception as e:
        # Handle any other unexpected errors
        if conn:
            conn.close()
        return handle_general_error(e, operation="save_brew")

@app.route('/poll')
def poll_form():
    """Renders the poll form with a unique ID."""
    unique_id = str(uuid.uuid4())
    return render_template('poll.html', unique_id=unique_id)

@app.route('/poll/<poll_id>')
def poll_with_id(poll_id):
    """Renders the poll form with the provided ID, or a message if already submitted or invalid."""
    conn = get_general_db_connection()
    cursor = conn.cursor()
    
    # First check if the poll_id is a valid token in the tokens table
    cursor.execute("SELECT 1 FROM tokens WHERE token_ID = ?", (poll_id,))
    is_valid_token = cursor.fetchone()
    
    if not is_valid_token:
        conn.close()
        return render_template('message.html', message='הסקר אינו תקף, אנא פנה למנהל המערכת') # Error for invalid token
      # If token is valid, check if poll was already submitted
    cursor.execute("SELECT 1 FROM poll WHERE poll_ID = ?", (poll_id,))
    already_submitted = cursor.fetchone()
    conn.close()

    if already_submitted:
        return render_template('message.html', message='הסקר כבר נשלח') # Message for already submitted poll
    else:
        return render_template('poll.html', unique_id=poll_id) # Render poll form with valid ID

@app.route('/submit_poll', methods=['POST'])
def submit_poll():
    """Handles the submission of the poll form."""
    import sys
    try:
        poll_id = request.form.get('poll_id')
        if not poll_id:
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': 'Poll ID is missing.'}), 400
            flash("Poll ID is missing. Please try again.", "danger")
            return redirect(url_for('poll_form'))

        # Extract rating questions (q1-q5)
        ratings = {}
        for i in range(1, 6):
            q_name = f'q{i}'
            try:
                rating = int(request.form.get(q_name, 0))
                if not (1 <= rating <= 10 if q_name != 'q5' else 0 <= rating <= 10):
                    if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                        return jsonify({'success': False, 'error': f'Invalid rating for question {i}'}), 400
                    flash(f"Invalid rating for question {i}. Please enter a value between 1 and 10 (or 0-10 for q5).", "danger")
                    return redirect(url_for('poll_form'))
                ratings[q_name] = rating
            except ValueError:
                if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                    return jsonify({'success': False, 'error': f'Invalid input for question {i}'}), 400
                flash(f"Invalid input for question {i}. Please enter a number.", "danger")
                return redirect(url_for('poll_form'))

        # Extract checkbox values for features (cb1-cb5)
        selected_features = request.form.getlist('features')
        feature_mapping = {
            "בתי קפה מעניינים בעולם": "cb1",
            "החלפת פולי קפה": "cb2",
            "מנוע חיפוש למכונות קפה": "cb3",
            "מעבדות וטכנאים למכונות קפה": "cb4",
            "מבצעים": "cb5"
        }
        feature_flags = {f"cb{i}": False for i in range(1, 6)}
        for feature_text in selected_features:
            for text, flag_name in feature_mapping.items():
                if feature_text == text:
                    feature_flags[flag_name] = True
                    break

        submission_date = datetime.now().strftime('%Y-%m-%d %H:%M:%S')

        conn = None
        try:
            conn = get_general_db_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO poll (
                    poll_ID, q1, q2, q3, q4, q5, cb1, cb2, cb3, cb4, cb5, submission_date
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                poll_id, ratings['q1'], ratings['q2'], ratings['q3'], ratings['q4'], ratings['q5'],
                feature_flags['cb1'], feature_flags['cb2'], feature_flags['cb3'],
                feature_flags['cb4'], feature_flags['cb5'], submission_date
            ))
            conn.commit()
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': True})
            session['poll_success'] = True
            return redirect('/')
        except Exception as e:
            if conn:
                conn.rollback()
            app.logger.error(f"Database error submitting poll: {e}", exc_info=True)
            if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
                return jsonify({'success': False, 'error': str(e)}), 500
            flash("An error occurred while submitting your poll. Please try again.", "danger")
            return redirect(url_for('poll_form'))
        finally:
            if conn:
                conn.close()

    except Exception as e:
        app.logger.error(f"Unexpected error in submit_poll: {e}", exc_info=True)
        if request.headers.get('X-Requested-With') == 'XMLHttpRequest':
            return jsonify({'success': False, 'error': str(e)}), 500
        flash("An unexpected error occurred. Please try again.", "danger")
        return redirect(url_for('poll_form'))

@app.route('/success')
def success():
    return render_template('success.html')

@app.route("/search_beans", methods=["GET"])
def search_beans():
    # Allow both logged in and unregistered users to access search_beans
    # But only from the main page (index.html), not direct URL access for unregistered users

    # Check if user is logged in
    user_logged_in = bool(g.user)

    # For unregistered users, check referrer to prevent direct URL access
    if not user_logged_in:
        referrer = request.referrer
        # If no referrer or not from our site, redirect to home
        if not referrer or not referrer.startswith(request.host_url):
            flash("Please access search from the main page.", "warning")
            return redirect(url_for('home'))

        # Check if referrer is from allowed pages (index or home)
        allowed_paths = ['/', '/index', '/index.html']
        referrer_path = referrer.replace(request.host_url, '').split('?')[0].rstrip('/') or '/'

        if referrer_path not in allowed_paths:
            flash("Please access search from the main page.", "warning")
            return redirect(url_for('home'))

    conn = get_db_connection()
    cursor = conn.cursor()
    cursor.execute("SELECT country FROM countries ORDER BY country")
    countries = cursor.fetchall()
    cursor.execute("SELECT c_flavor FROM coffee_flavors ORDER BY c_flavor")
    flavors = cursor.fetchall()
    # Add roasters query
    cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
    roasters = cursor.fetchall()
    # Add cities query
    cursor.execute("SELECT c_city FROM city_list ORDER BY c_city")
    cities = cursor.fetchall()
    conn.close()

    show_login_modal = not user_logged_in  # If user is not logged in, flag to open login modal

    return render_template("search_beans.html",
                           countries=countries, flavors=flavors, roasters=roasters, cities=cities,
                           show_login_modal=show_login_modal, user_logged_in=user_logged_in)



@app.route('/filter_beans', methods=['POST'])
def filter_beans():
    # Allow both logged in and unregistered users to filter beans
    # Unregistered users will get limited results (5 records max)

    data = request.get_json()

    # Check if user is logged in
    user_logged_in = bool(g.user)

    # Normalize roasters field to always be a list
    roasters = data.get('roasters', [])
    if isinstance(roasters, str):
        roasters = [roasters]

    # Get cities and fetch roasters for those cities if needed
    cities = data.get('cities', [])
    if cities and not roasters:
        # If cities are selected but no roasters, fetch roasters for those cities
        conn = get_db_connection()
        cursor = conn.cursor()
        roaster_ids = []
        for city in cities:
            cursor.execute("SELECT roaster_id FROM roasters WHERE city = ?", (city,))
            city_roasters = cursor.fetchall()
            roaster_ids.extend([str(r['roaster_id']) for r in city_roasters])
        conn.close()

        # Add these roasters to the filter
        if roaster_ids:
            roasters = roaster_ids

    origin = data.get('origin', [])
    processing = data.get('processing', '')
    elevation = data.get('elevation', [])
    flavors = data.get('flavors', [])
    roast_level = data.get('roast_level', '')
    arabica_selected = int(data.get('arabica', 0))
    robusta_selected = int(data.get('robusta', 0))
    singleorigin = int(data.get('singleorigin', 0)) # Add single-origin parameter
    mix = int(data.get('mix', 0))
    speciality = int(data.get('speciality', 0))
    decaf = data.get('decaf', 0)
    selected_brew_methods = data.get('brew_methods', []) # Expect a list of strings
    # New parameter for flavor selection mode. Default is OR.
    flavor_mode = data.get('flavor_mode', 'OR').upper()

    query = """
        SELECT
            beans.bean_id,
            beans.roaster_id,
            beans.bean_name,
            beans.origin,
            beans.processing,
            beans.elevation,
            beans.body,
            beans.acidity,
            beans.flavors,
            beans.roast_level,
            beans.arabica,
            beans.robusta,
            beans.mix,
            beans.price,
            beans.weight,
            beans.turkish,
            beans.espresso,
            beans.french_press,
            beans.pour_over,
            beans.drip,
            beans.cold_brew,
            beans.Speciality AS speciality,
            beans.SCA_score,
            beans.decaf,
            roasters.name AS roaster_name,
            roasters.webpage AS roaster_webpage
        FROM beans
        JOIN roasters ON beans.roaster_id = roasters.roaster_id
        WHERE 1=1
    """
    params = []

    if origin:
        stripped_origins = [o.strip() for o in origin]
        query += " AND (" + " OR ".join(["beans.origin LIKE ?"] * len(stripped_origins)) + ")"
        params.extend([f"%{o}%" for o in stripped_origins])
    if processing:
        query += " AND beans.processing = ?"
        params.append(processing)
    if flavors:
        joiner = " OR " if flavor_mode != "AND" else " AND "
        query += " AND (" + joiner.join(["beans.flavors LIKE ?"] * len(flavors)) + ")"
        params.extend([f"%{f}%" for f in flavors])
    if roast_level:
        query += " AND beans.roast_level = ?"
        params.append(roast_level)

    if roasters:
        roaster_ids = [int(r) for r in roasters]
        query += f" AND beans.roaster_id IN ({','.join(['?'] * len(roaster_ids))})"
        params.extend(roaster_ids)

    # Enhanced arabica/robusta filtering logic
    if arabica_selected and robusta_selected:
        # Both selected: return only beans that contain both arabica AND robusta
        query += " AND beans.arabica > 0 AND beans.robusta > 0"
    elif arabica_selected:
        # Only arabica selected: return pure arabica beans (no robusta)
        query += " AND beans.arabica > 0 AND beans.robusta = 0"
    elif robusta_selected:
        # Only robusta selected: return pure robusta beans (no arabica)
        query += " AND beans.robusta > 0 AND beans.arabica = 0"

    if singleorigin == 1:
        query += " AND beans.mix = 0"  # Single-origin means mix is 0
    if mix == 1:
        query += " AND beans.mix = 1"
    if speciality:
        query += " AND beans.speciality = 1"
    if decaf:
        query += " AND beans.decaf = 1"

    # selected_brew_methods and its log are assumed to be immediately before this block
    valid_brew_methods = ['turkish', 'espresso', 'french_press', 'pour_over', 'drip', 'cold_brew']
    active_brew_methods = [m for m in selected_brew_methods if m in valid_brew_methods]
    # Ensure the log for active_brew_methods is present if it was added in a previous step, or add it:

    if not active_brew_methods:
        # If no valid brew methods are selected (e.g., selected_brew_methods was empty or contained only invalid values)
        query += " AND 1=0" # Ensures no beans are returned
    else:
        # If there are active brew methods, filter by any of them (OR logic)
        brew_conditions = []
        for method in active_brew_methods:
            brew_conditions.append(f"beans.{method} = 1")
        
        if brew_conditions:
            query += f" AND ({' OR '.join(brew_conditions)})"

    if elevation:
        if elevation == 'altd_1':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(0)
            params.append(1000)
        elif elevation == 'altd_2':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(1001)
            params.append(1500)
        elif elevation == 'altd_3':
            query += " AND CAST(beans.elevation AS INTEGER) > ? AND CAST(beans.elevation AS INTEGER) < ?"
            params.append(1501)
            params.append(2000)
        elif elevation == 'altd_4':
            query += " AND CAST(beans.elevation AS INTEGER) > ?"
            params.append(2001)

    sort_by = data.get('sort_by')
    # Validate sort_dir against an allowlist
    sort_dir_input = data.get('sort_dir', 'asc').upper()
    sort_dir = 'ASC' if sort_dir_input not in ('ASC', 'DESC') else sort_dir_input

    if sort_by in ('arabica', 'robusta'):
        # Use the validated sort_dir
        query += f" ORDER BY beans.{sort_by} {sort_dir}"

    # Add LIMIT for unregistered users (limit to 5 records)
    if not user_logged_in:
        query += " LIMIT 5"

    conn = get_db_connection()
    cursor = conn.cursor()
    try:
        cursor.execute(query, params)
        rows = cursor.fetchall()
        beans = []
        for row in rows:
            beans.append({
                "bean_id": row["bean_id"],
                "roaster_id": row["roaster_id"],
                "bean_name": row["bean_name"],
                "origin": row["origin"],
                "processing": row["processing"],
                "elevation": row["elevation"],
                "body": row["body"],  # Include body field
                "acidity": row["acidity"],  # Include acidity field
                "flavors": row["flavors"],
                "roast_level": row["roast_level"],
                "arabica": row["arabica"],
                "robusta": row["robusta"],
                "mix": row["mix"],
                "price": row["price"],
                "weight": row["weight"],
                "turkish": row["turkish"],
                "espresso": row["espresso"],
                "french_press": row["french_press"],
                "pour_over": row["pour_over"],
                "drip": row["drip"],
                "cold_brew": row["cold_brew"],
                "speciality": row["speciality"],
                "SCA_score": row["SCA_score"],  # Ensure SCA_score is included
                "roaster_name": row["roaster_name"],
                "roaster_webpage": row["roaster_webpage"]  # Include roaster_webpage
            })

        conn.close()
        return jsonify(beans)
    except Exception as e:
        app.logger.error(f"Error filtering beans: {str(e)}", exc_info=True)
        if 'conn' in locals() and conn:
            try:
                conn.close()
            except Exception as db_close_e:
                app.logger.error(f"Error closing DB connection during exception handling: {db_close_e}")
        return jsonify({"error": "An internal error occurred while filtering beans."}), 500

@app.route("/test_static")
def test_static():
    """Simple test endpoint to verify static files are being served correctly"""
    try:
        app.logger.info("test_static endpoint called")

        # Get the absolute path to the static folder
        static_folder = app.static_folder
        app.logger.info(f"Static folder: {static_folder}")

        # Create a simple HTML response
        html = """
        <html>
        <head>
            <title>Static File Test</title>
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
        </head>
        <body>
            <h1>Static File Test</h1>
            <p>Testing if static files are being served correctly.</p>

            <h2>Testing direct URL access:</h2>
            <p>Click to test: <a href="/static/js/update_results.js" target="_blank">/static/js/update_results.js</a></p>
            <p>Click to test: <a href="/static/test.txt" target="_blank">/static/test.txt</a></p>

            <h2>Testing with fetch API:</h2>
            <div id="fetch-result">Testing...</div>

            <script>
                // Test with absolute path
                fetch('/static/js/update_results.js')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to load update_results.js: ${response.status} ${response.statusText}`);
                        }
                        document.getElementById('fetch-result').innerHTML += '<p style="color:green">Successfully loaded update_results.js</p>';
                    })
                    .catch(error => {
                        document.getElementById('fetch-result').innerHTML += `<p style="color:red">${error.message}</p>`;
                    });

                // Test with test.txt
                fetch('/static/test.txt')
                    .then(response => {
                        if (!response.ok) {
                            throw new Error(`Failed to load test.txt: ${response.status} ${response.statusText}`);
                        }
                        document.getElementById('fetch-result').innerHTML += '<p style="color:green">Successfully loaded test.txt</p>';
                    })
                    .catch(error => {
                        document.getElementById('fetch-result').innerHTML += `<p style="color:red">${error.message}</p>`;
                    });
            </script>
        </body>
        </html>
        """
        return html
    except Exception as e:
        app.logger.error(f"Error in test_static: {str(e)}")
        return f"Error: {str(e)}", 500

@app.route('/test_simple')
def test_simple():
    """A very simple test endpoint that doesn't rely on any static files"""
    try:
        app.logger.info("test_simple endpoint called")
        return """
        <html>
        <head>
            <title>Simple Test</title>
        </head>
        <body>
            <h1>Simple Test</h1>
            <p>This is a simple test page that doesn't rely on any static files.</p>
            <p>If you can see this, the Flask application is working correctly.</p>
        </body>
        </html>
        """
    except Exception as e:
        app.logger.error(f"Error in test_simple: {str(e)}")
        return f"Error: {str(e)}", 500

@app.route('/fallback/update_results.js')
def serve_update_results_js():
    """Fallback route to serve the update_results.js file directly"""
    try:
        app.logger.info("serve_update_results_js endpoint called")
        js_path = os.path.join(app.static_folder, 'js', 'update_results.js')

        if os.path.isfile(js_path):
            with open(js_path, 'r') as f:
                js_content = f.read()

            app.logger.info(f"Successfully read update_results.js, size: {len(js_content)} bytes")
            return js_content, 200, {'Content-Type': 'application/javascript'}
        else:
            app.logger.error(f"update_results.js not found at {js_path}")
            return "// File not found", 404, {'Content-Type': 'application/javascript'}
    except Exception as e:
        app.logger.error(f"Error serving update_results.js: {str(e)}")
        return f"// Error: {str(e)}", 500, {'Content-Type': 'application/javascript'}

@app.route('/get_roaster_details/<int:roaster_id>')
def get_roaster_details(roaster_id):
    try:
        conn = sqlite3.connect(DATABASE)
        cursor = conn.cursor()
        cursor.execute("""
            SELECT
                name,
                COALESCE(address, '') as address,
                COALESCE(city, '') as city,
                COALESCE(zip, '') as zip,
                COALESCE(email, '') as email,
                COALESCE(webpage, '') as webpage,
                COALESCE(minimun_shipping, '') as minimun_shipping,
                COALESCE(shipping_cost, '') as shipping_cost
            FROM roasters
            WHERE roaster_id = ?
        """, (roaster_id,))
        row = cursor.fetchone()
        cursor.close()
        conn.close()
        if row:
            data = {
                'name': row[0],
                'address': row[1] or 'לא זמין',
                'city': row[2] or 'לא זמין',
                'zip': row[3] or 'לא זמין',
                'email': row[4] or 'לא זמין',
                'website': row[5] or 'לא זמין',
                'minimum_shipping': row[6] or 'לא זמין',
                'shipping_cost': row[7] or 'לא זמין'
            }
            return jsonify(data)
        return jsonify({'error': 'Roaster not found'}), 404
    except Exception as e:
        app.logger.error(f"Database error in get_roaster_details: {str(e)}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return jsonify({'error': 'Database error', 'message': str(e)}), 500

# Fix the show_brew route and other errors

@app.route('/show_brew', methods=['GET'])
@login_required
def show_brew():
    """
    Display the user's brew logs.
    Requires user to be logged in.
    """
    from utils.error_handler import handle_database_error, handle_general_error

    uid = session.get('uid')
    conn = None

    try:
        if not uid:
            # If no UID in session, just show an empty page
            return render_template('showBrew.html', brewlogs=[], error="No user ID found in session")

        try:
            conn = get_db_connection()
            cursor = conn.cursor()

            # Use a parameterized query with no string formatting
            query = '''
                SELECT
                    brewlog.*,
                    beans.bean_name,
                    beans.origin,
                    beans.processing,
                    beans.roast_level,
                    roasters.name as roaster_name
                FROM
                    brewlog
                JOIN
                    beans ON brewlog.bean_id = beans.bean_id
                JOIN
                    roasters ON beans.roaster_id = roasters.roaster_id
                WHERE
                    brewlog.uid = ?
                ORDER BY
                    substr(brewlog.brew_date, 7, 4) || '-' || substr(brewlog.brew_date, 4, 2) || '-' || substr(brewlog.brew_date, 1, 2) DESC
            '''

            # Always convert uid to string for consistency
            str_uid = str(uid)
            cursor.execute(query, (str_uid,))
            brew_records = cursor.fetchall()

            brewlogs = [dict(row) for row in brew_records]

            # Reformat date fields to dd-mm-YY (last digit of year)
            for brewlog in brewlogs:
                # Process brew_date
                if 'brew_date' in brewlog and brewlog['brew_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['brew_date'].split('-')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['brew_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

                # Process bean_roasting_date
                if 'bean_roasting_date' in brewlog and brewlog['bean_roasting_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['bean_roasting_date'].split('/')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['bean_roasting_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

                # Process bean_purchase_date
                if 'bean_purchase_date' in brewlog and brewlog['bean_purchase_date']:
                    try:
                        # Assuming date format is dd-mm-yyyy
                        parts = brewlog['bean_purchase_date'].split('/')
                        if len(parts) == 3 and len(parts[2]) == 4:  # Make sure it's a valid date with 4-digit year
                            # Keep only the last digit of the year
                            brewlog['bean_purchase_date'] = f"{parts[0]}-{parts[1]}-{parts[2][-2:]}"
                    except Exception:
                        # Just keep the original format if there's an error
                        pass

            conn.close()
            return render_template('showBrew.html', brewlogs=brewlogs)

        except sqlite3.Error as db_error:
            # Handle database errors
            if conn:
                conn.close()
            # For template rendering, we'll handle the error differently than API endpoints
            error_message = "Database error occurred while fetching your brew logs."
            return render_template('showBrew.html', brewlogs=[], error=error_message)

    except Exception as e:
        # Handle any other unexpected errors
        if conn:
            conn.close()
        # For template rendering, we'll handle the error differently than API endpoints
        error_message = "An unexpected error occurred while fetching your brew logs."
        return render_template('showBrew.html', brewlogs=[], error=error_message)

# Fix the check_session endpoint
@app.route('/check_session', methods=['GET'])
def check_session():
    """Check if user is authenticated in the current session"""
    try:
        if g.user:
            return jsonify({'authenticated': True, 'user': g.user.display_name})
        else:
            return jsonify({'authenticated': False})
    except Exception as e:
        app.logger.error(f"Error in check_session: {str(e)}", exc_info=True)
        return jsonify({'authenticated': False, 'error': "An internal error occurred."})

@app.route('/get_firebase_config', methods=['GET'])
def get_firebase_config():
    """Provide Firebase configuration to the client, ensuring environment variables are set."""
    api_key = os.environ.get('FIREBASE_API_KEY')
    auth_domain = os.environ.get('FIREBASE_AUTH_DOMAIN')
    project_id = os.environ.get('FIREBASE_PROJECT_ID')

    if not all([api_key, auth_domain, project_id]):
        app.logger.error("Error: Missing one or more Firebase environment variables (FIREBASE_API_KEY, FIREBASE_AUTH_DOMAIN, FIREBASE_PROJECT_ID)") # Changed to app.logger.error
        return jsonify({"error": "Server configuration error: Missing Firebase credentials."}), 500

    config = {
        'apiKey': api_key,
        'authDomain': auth_domain,
        'projectId': project_id
    }
    # Add other necessary config values if needed, loaded from env vars
    return jsonify(config)

@app.route('/get_csrf_token', methods=['GET'])
def get_csrf_token():
    """Provide CSRF token for frontend requests"""
    from flask_wtf.csrf import generate_csrf
    return jsonify({'csrf_token': generate_csrf()})



@app.route('/set_roaster_id', methods=['POST'])
@login_required
def set_roaster_id():
    roaster_id = request.form.get('roaster_id')
    if not roaster_id:
        return "Missing roaster_id", 400
    session['current_roaster_id'] = roaster_id
    return redirect(url_for('manage_roaster'))

@app.route('/manage_roaster')
@login_required
def manage_roaster():
    # Get roaster_id from session
    roaster_id = session.get('roaster_id') # Use the key set by before_request
    if not roaster_id:
        return redirect(url_for('home'))
    # Set current_roaster_id in session for edit_roaster
    session['current_roaster_id'] = roaster_id
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        # Get roaster details
        cursor.execute("""
            SELECT
                name, address, city, zip, email, webpage,
                minimun_shipping, shipping_cost, uid
            FROM roasters
            WHERE roaster_id = ?
        """, (roaster_id,))
        roaster = cursor.fetchone()

        if not roaster:
            conn.close()
            return "Roaster not found", 404

        # Get this roaster's beans
        cursor.execute("""
            SELECT
                bean_id, bean_name, origin, processing,
                flavors, acidity, after_taste, roast_level,
                body, arabica, robusta, mix, price, weight,
                Speciality, SCA_score, decaf, image_file
            FROM beans
            WHERE roaster_id = ?
            ORDER BY bean_name
        """, (roaster_id,))
        beans = cursor.fetchall()

        conn.close()

        return render_template(
            'manage_roaster.html',
            roaster=roaster,
            beans=beans,
            roaster_id=roaster_id
        )

    except Exception as e:
        app.logger.error(f"Error in manage_roaster: {e}", exc_info=True)
        if 'conn' in locals():
            conn.close()
        return f"Error loading roaster data: {str(e)}", 500

@app.route('/link_roaster', methods=['GET', 'POST'])
@admin_required # Changed from @login_required; admin check is now handled by the decorator
def link_roaster():
    # The @admin_required decorator handles the permission check.
    # Old placeholder comments for admin check can be removed or kept as historical.

    # Initialize connection to None
    conn = None
    try:
        conn = get_db_connection()
        cursor = conn.cursor()

        if request.method == 'POST':
            data = request.get_json() # Get data from JSON body
            email = data.get('email')
            roaster_id = data.get('roaster_id')
            unlink_roaster = data.get('unlink_roaster', False) # Get the checkbox value

            if not email or not roaster_id:
                flash("Email and Roaster ID are required.", "danger")
                return jsonify({'success': False, 'message': 'Email and Roaster ID are required.'}), 400

            user_uid = None
            try:
                user = auth.get_user_by_email(email)
                user_uid = user.uid
            except auth.UserNotFoundError:
                flash(f"User with email {email} not found in Firebase.", "danger")
                return jsonify({'success': False, 'message': f"User with email {email} not found."}), 404
            except Exception as e:
                app.logger.error(f"Error fetching user from Firebase: {e}", exc_info=True)
                flash("An error occurred while fetching user details.", "danger")
                return jsonify({'success': False, 'message': 'An error occurred while fetching user details.'}), 500

            try:
                if unlink_roaster:
                    # Unlinking logic
                    cursor.execute("SELECT uid FROM roasters WHERE roaster_id = ?", (roaster_id,))
                    current_uid = cursor.fetchone()

                    if current_uid and current_uid['uid'] == user_uid:
                        cursor.execute("UPDATE roasters SET uid = NULL WHERE roaster_id = ?", (roaster_id,))
                        conn.commit()
                        flash(f"Roaster ID {roaster_id} successfully unlinked from {email}.", "success")
                        return jsonify({'success': True, 'message': f"Roaster ID {roaster_id} successfully unlinked from {email}."}), 200
                    elif current_uid and current_uid['uid'] != user_uid:
                        flash(f"Roaster ID {roaster_id} is linked to a different user. Cannot unlink.", "danger")
                        return jsonify({'success': False, 'message': f"Roaster ID {roaster_id} is linked to a different user. Cannot unlink."}), 403
                    else:
                        flash(f"Roaster ID {roaster_id} is not currently linked to any user.", "info")
                        return jsonify({'success': False, 'message': f"Roaster ID {roaster_id} is not currently linked to any user."}), 404
                else:
                    # Existing linking logic
                    # Check if roaster is already linked to another UID
                    cursor.execute("SELECT uid FROM roasters WHERE roaster_id = ?", (roaster_id,))
                    existing_uid = cursor.fetchone()
                    if existing_uid and existing_uid['uid'] and existing_uid['uid'] != user_uid:
                        flash(f"Roaster ID {roaster_id} is already linked to another user.", "danger")
                        return jsonify({'success': False, 'message': f"Roaster ID {roaster_id} is already linked to another user."}), 409

                    # Link roaster to UID
                    cursor.execute("UPDATE roasters SET uid = ? WHERE roaster_id = ?", (user_uid, roaster_id))
                    conn.commit()

                    # Fetch roaster's name for the email
                    cursor.execute("SELECT name FROM roasters WHERE roaster_id = ?", (roaster_id,))
                    roaster_data = cursor.fetchone()

                    if roaster_data:
                        roaster_name = roaster_data['name']
                        
                        # The recipient email is the one selected from the form
                        roaster_email = email

                        # Read the HTML email template
                        try:
                            with open('new_letter.html', 'r', encoding='utf-8') as f:
                                email_html = f.read()
                            
                            # Personalize the email
                            email_html = email_html.replace('{{roaster_name}}', roaster_name)
                            email_html = email_html.replace('{{roaster_email}}', roaster_email) # This uses the user's email

                            # Send the email
                            email_subject = "ברוכים הבאים לאתר קפה ישראלי"
                            send_mail(
                                recipient=roaster_email,
                                subject=email_subject,
                                message=email_html
                            )
                            flash(f"Roaster ID {roaster_id} successfully linked to user ID {user_uid} and a welcome email was sent to {roaster_email}.", "success")
                        except Exception as e:
                            app.logger.error(f"Failed to send welcome email to {roaster_email}: {e}")
                            flash(f"Roaster ID {roaster_id} linked, but failed to send welcome email.", "warning")
                    else:
                        flash(f"Roaster ID {roaster_id} linked, but could not retrieve roaster name to send welcome email.", "warning")

                    return jsonify({'success': True, 'message': f"Roaster ID {roaster_id} successfully linked to {email}."}), 200

            except Exception as e:
                conn.rollback()
                app.logger.error(f"Error processing roaster link/unlink: {e}", exc_info=True)
                flash("An error occurred while processing the roaster link/unlink.", "danger")
                return jsonify({'success': False, 'message': 'An error occurred while processing the roaster link/unlink.'}), 500

        # GET request: Fetch roasters to display in the dropdown
        form = LinkRoasterForm() # Instantiate the form
        
        # Fetch all registered users from Firebase
        try:
            user_list = []
            page = auth.list_users()
            while page:
                for user in page.users:
                    if user.email:  # Only include users with email addresses
                        user_list.append(user.email)
                page = page.get_next_page()
            
            user_list.sort()
            form.email.choices = [(email, email) for email in user_list]
            
        except Exception as e:
            app.logger.error(f"Error fetching users from Firebase: {e}", exc_info=True)
            flash("Error fetching user list from Firebase.", "danger")
            form.email.choices = []  # Empty list if there's an error
        
        # Fetch roasters for the dropdown
        cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
        roasters = cursor.fetchall()
        
        # Pass the form object to the template
        return render_template('link_roaster.html', roasters=roasters, form=form)

    finally:
        if conn:
            conn.close()

# Define a simple form for CSRF protection in the link_roaster route
class LinkRoasterForm(FlaskForm):
    email = SelectField('Email', choices=[])

# Replace the display_roaster route to use token instead of integer
@app.route('/display_roaster/<token>')
def display_roaster(token):
    secret_key = os.environ.get("FLASK_SECRET_KEY", "a_default_secret_key_for_development")
    try:
        roaster_id = (decrypt_id(secret_key, token)/8457)
    except Exception:
        return "Invalid roaster token", 400

    # Use the DATABASE variable for the path for me
    conn = sqlite3.connect(DATABASE)
    cursor = conn.cursor()
    cursor.execute("SELECT roaster_id, name, email FROM roasters WHERE roaster_id = ?", (roaster_id,))
    row = cursor.fetchone()
    if not row:
        conn.close()
        return "Roaster not found", 404
    roaster = {'roaster_id': row[0], 'name': row[1], 'email': row[2]}

    cursor.execute("SELECT * FROM beans WHERE roaster_id = ?", (roaster_id,))
    beans_rows = cursor.fetchall()
    # Map beans_rows to dicts as needed for your template
    beans = [
        {
            'bean_name': r[2],
            'origin': r[3],
            'flavors': r[6],
            'roast_level': r[9],
            'arabica': r[11],
            'robusta': r[12],
            'mix': r[19],
            'speciality': r[24],
            'decaf': r[25],
            'price': r[20],
            'weight': r[21]
        }
        for r in beans_rows
    ]
    conn.close()
    return render_template('display_roaster.html', roaster=roaster, beans=beans)

@app.route('/edit_roaster', methods=['GET', 'POST'])
@login_required
def edit_roaster():
    # Get roaster_id from session only (do not use request.args)
    roaster_id = session.get('current_roaster_id')
    if not roaster_id:
        return redirect(url_for('home'))

    # Verify user is authorized for this roaster
    if not session.get('roaster_user') or session.get('roaster_id') != roaster_id:
        return redirect(url_for('home'))

    conn = get_db_connection()
    cursor = conn.cursor()

    # Get roaster details for editing
    cursor.execute("""
        SELECT
            name, address, city, zip, email, webpage,
            minimun_shipping, shipping_cost, uid
        FROM roasters
        WHERE roaster_id = ?
    """, (roaster_id,))
    roaster = cursor.fetchone()
    conn.close()

    if not roaster:
        return "Roaster not found", 404

    if request.method == 'POST':
        # Update roaster details
        name = request.form['name']
        address = request.form['address']
        city = request.form['city']
        zip = request.form['zip']
        email = request.form['email']
        webpage = request.form['webpage']
        minimun_shipping = request.form['minimun_shipping']
        shipping_cost = request.form['shipping_cost']

        conn = get_db_connection()
        cursor = conn.cursor()
        cursor.execute('''
            UPDATE roasters SET
                name = ?, address = ?, city = ?, zip = ?, email = ?,
                webpage = ?, minimun_shipping = ?, shipping_cost = ?
            WHERE roaster_id = ?
        ''', (name, address, city, zip, email, webpage, minimun_shipping, shipping_cost, roaster_id))
        conn.commit()
        conn.close()

        session['current_roaster_id'] = roaster_id
        return redirect(url_for('manage_roaster'))

    return render_template('edit_roaster.html', roaster=roaster, roaster_id=roaster_id)

@app.route('/send_contact_email', methods=['POST'])
def send_contact_email():
    if request.method == 'POST':
        # Retrieve form data
        name = request.form.get('name')
        email_from_form = request.form.get('email') # Renamed to avoid conflict
        subject_from_form = request.form.get('subject') # Renamed
        message_content = request.form.get('message') # Renamed

        # Validate form data
        if not all([name, email_from_form, subject_from_form, message_content]):
            return jsonify({
                'success': False,
                'message': "כל השדות הם חובה. אנא מלא את כל הפרטים."
            })

        # Pre-process message content for HTML (replace newlines with <br>)
        message_content_html = message_content.replace('\n', '<br>')

        # Construct HTML message with RTL support
        html_message = f"""
        <html>
          <head>
            <style>
              body {{ direction: rtl; text-align: right; }}
              p, h3 {{ text-align: right; }}
            </style>
          </head>
          <body>
            <h3>פנייה חדשה מטופס יצירת קשר</h3>
            <p><strong>שם משתמש</strong> {name}</p>
            <p><a href="mailto:{email_from_form}">{email_from_form}</a> <strong>:דוא"ל</strong></p>
            <hr>
            <p><strong>נושא</strong> {subject_from_form}</p>
            <p><strong>הודעה</strong></p>
            <p>{message_content_html}</p>
          </body>
        </html>
        """

        recipient_email = "<EMAIL>"

        try:
            email_sent = send_email(
                recipient=recipient_email,
                subject=subject_from_form,
                message=html_message
            )

            if email_sent:
                response = jsonify({
                    'success': True,
                    'message': 'ההודעה נשלחה בהצלחה! נחזור אליך בהקדם.',
                    'redirect_url': url_for('home')
                })
                response.headers['Content-Type'] = 'application/json'
                return response, 200
            else:
                return jsonify({
                    'success': False,
                    'message': "אירעה שגיאה בשליחת ההודעה. אנא נסה שוב מאוחר יותר."
                })

        except Exception as e:
            app.logger.error(f"Error sending contact form email: {str(e)}", exc_info=True)
            return jsonify({
                'success': False,
                'message': "אירעה שגיאה בשליחת ההודעה. אנא נסה שוב מאוחר יותר."
            })

    return jsonify({
        'success': False,
        'message': "Invalid request method."
    })

@app.route('/roaster_letter')
def roaster_letter():
    return render_template('roaster_letter.html')

@app.route('/api/roasters_csv')
def api_roasters_csv():
    csv_path = os.path.join(os.path.dirname(__file__), 'utils', 'roasters links.csv')
    roasters = []
    with open(csv_path, encoding='utf-8-sig') as f:
        reader = csv.DictReader(f)
        for row in reader:
            roasters.append({
                'roaster_id': row['roaster_id'],
                'roaster_name': row['roaster name'],
                'roaster_email': row['roaster email'],
                'roaster_link': row['roaster link']
            })
    return jsonify(roasters)

@app.route('/api/israeli_coffee_letter')
def api_israeli_coffee_letter():
    letter_path = os.path.join(os.path.dirname(__file__), 'israeli_coffee_letter.txt')
    with open(letter_path, encoding='utf-8') as f:
        paragraphs = [p.strip() for p in f.read().split('\n\n') if p.strip()]
    return jsonify(paragraphs)

# Add a new route to show the email verification notice
@app.route('/verify-email')
def verify_email_notice():
    # Check if user is logged in but not verified
    user = getattr(g, 'user', None)
    if not user:
        # Not logged in, redirect home
        return redirect(url_for('home'))
    if user.email_verified:
        # Already verified, redirect home
        flash("Your email is already verified.", "success")
        return redirect(url_for('home'))

    return render_template('verify_email.html')

# New backend route for sending email verification
@app.route('/send-verification-email', methods=['POST'])
@csrf.exempt  # Exempt from CSRF protection for registration flow
def send_verification_email():
    """Send email verification link via backend instead of Firebase"""
    try:
        # Manual authentication check instead of @login_required decorator
        # This allows unverified users to request verification emails
        if 'id_token' not in session:
            return jsonify({'success': False, 'message': 'User not authenticated'}), 401

        try:
            # Verify the token and get user info
            token_details = auth.verify_id_token(session['id_token'], clock_skew_seconds=60)
            user = auth.get_user(token_details['uid'])
        except Exception as e:
            app.logger.error(f"Error verifying user token in send_verification_email: {e}")
            return jsonify({'success': False, 'message': 'Invalid authentication'}), 401

        if not user:
            return jsonify({'success': False, 'message': 'User not found'}), 401

        if user.email_verified:
            return jsonify({'success': False, 'message': 'Email already verified'}), 400

        # Check if FLASK_SECRET_KEY is available for token generation
        flask_secret_key = os.environ.get('FLASK_SECRET_KEY')
        if not flask_secret_key:
            return jsonify({'success': False, 'message': 'Server configuration error'}), 500

        # Generate verification token
        try:
            token = generate_email_verification_token(user.email, user.uid)
        except Exception:
            return jsonify({'success': False, 'message': 'Failed to generate verification token'}), 500

        # Create verification URL
        verification_url = url_for('verify_email_backend', token=token, _external=True)

        # Create email content
        try:
            from utils.email_actions import create_verification_email_html
            email_html = create_verification_email_html(verification_url, user.email)
        except Exception:
            return jsonify({'success': False, 'message': 'Failed to generate email template'}), 500

        # Send email
        try:
            email_sent = send_email(
                recipient=user.email,
                subject="Israeli Coffee - Verify Your Email Address",
                message=email_html
            )
        except Exception:
            return jsonify({'success': False, 'message': 'Email sending failed'}), 500

        if email_sent:
            return jsonify({'success': True, 'message': 'Verification email sent successfully'})
        else:
            return jsonify({'success': False, 'message': 'Failed to send verification email'}), 500

    except Exception as e:
        app.logger.error(f"Error sending verification email: {e}", exc_info=True)
        return jsonify({'success': False, 'message': 'An error occurred while sending verification email'}), 500

# New backend route for handling email verification
@app.route('/verify-email/<token>')
def verify_email_backend(token):
    """Handle email verification when user clicks the link"""
    try:
        from utils.email_actions import verify_email_token, EmailActionError

        # Verify the token
        payload = verify_email_token(token)
        email = payload['email']
        uid = payload['uid']

        # Get the user from Firebase
        try:
            user = auth.get_user(uid)
            if user.email != email:
                flash("Invalid verification link - email mismatch", "danger")
                return redirect(url_for('home'))

            # Mark email as verified in Firebase
            auth.update_user(uid, email_verified=True)

            flash("Email verified successfully! You can now access all features.", "success")
            app.logger.info(f"Email verified for user {email}")

            return redirect(url_for('home'))

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error verifying email: {e}", exc_info=True)
        flash("An error occurred during email verification", "danger")
        return redirect(url_for('home'))

# New backend route for password reset form
@app.route('/reset-password/<token>')
def reset_password_form(token):
    """Display password reset form when user clicks the reset link"""
    try:
        from utils.email_actions import verify_password_reset_token, EmailActionError

        # Verify the token
        payload = verify_password_reset_token(token)
        email = payload['email']
        uid = payload['uid']

        # Verify user still exists
        try:
            user = auth.get_user(uid)
            if user.email != email:
                flash("Invalid password reset link - email mismatch", "danger")
                return redirect(url_for('home'))

            # Token is valid, show the password reset form
            return render_template('reset_password_form.html', token=token, email=email)

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error displaying password reset form: {e}", exc_info=True)
        flash("An error occurred while accessing the password reset form", "danger")
        return redirect(url_for('home'))

# New backend route for processing password reset
@app.route('/update-password', methods=['POST'])
def update_password():
    """Process the new password from the reset form"""
    try:
        from utils.email_actions import verify_password_reset_token, EmailActionError

        token = request.form.get('token')
        new_password = request.form.get('new_password')
        confirm_password = request.form.get('confirm_password')

        if not all([token, new_password, confirm_password]):
            flash("All fields are required", "danger")
            return redirect(url_for('reset_password_form', token=token))

        if new_password != confirm_password:
            flash("Passwords do not match", "danger")
            return redirect(url_for('reset_password_form', token=token))

        if len(new_password) < 6:
            flash("Password must be at least 6 characters long", "danger")
            return redirect(url_for('reset_password_form', token=token))

        # Verify the token
        payload = verify_password_reset_token(token)
        email = payload['email']
        uid = payload['uid']

        # Update the password in Firebase
        try:
            auth.update_user(uid, password=new_password)
            flash("Password updated successfully! Please log in with your new password.", "success")
            app.logger.info(f"Password reset completed for user {email}")
            # Redirect to home page with a login prompt instead of assuming user is logged in
            return redirect(url_for('home', password_reset='success'))

        except auth.UserNotFoundError:
            flash("User not found", "danger")
            return redirect(url_for('home'))
        except Exception as firebase_error:
            app.logger.error(f"Firebase error updating password for {email}: {firebase_error}")
            flash("Error updating password. Please try again.", "danger")
            return redirect(url_for('reset_password_form', token=token))

    except EmailActionError as e:
        flash(str(e), "danger")
        return redirect(url_for('home'))
    except Exception as e:
        app.logger.error(f"Error updating password: {e}", exc_info=True)
        flash("An error occurred while updating your password", "danger")
        return redirect(url_for('home'))

@app.route('/edit_price', methods=['GET', 'POST'])
@login_required
def edit_price():
    # Get all roasters for the dropdown
    conn = get_db_connection()
    cursor = conn.cursor()

    # Always show all roasters
    cursor.execute("SELECT roaster_id, name FROM roasters ORDER BY name")
    roasters = cursor.fetchall()

    # Get the selected roaster_id from the request
    selected_roaster_id = request.args.get('roaster_id')
    if request.method == 'POST':
        selected_roaster_id = request.form.get('roaster_id')

    beans = []
    selected_roaster_name = ""

    # If a roaster is selected, get its beans
    if selected_roaster_id:
        # Get roaster name
        cursor.execute("SELECT name FROM roasters WHERE roaster_id = ?", (selected_roaster_id,))
        roaster_row = cursor.fetchone()
        if roaster_row:
            selected_roaster_name = roaster_row['name']

        # Get beans for this roaster
        cursor.execute("""
            SELECT bean_id, bean_name, weight, price
            FROM beans
            WHERE roaster_id = ?
            ORDER BY bean_name
        """, (selected_roaster_id,))
        beans = cursor.fetchall()

    # Handle form submission to update prices
    if request.method == 'POST' and 'bean_ids[]' in request.form:
        # Authorization check: Only roaster users can update prices
        if not session.get('roaster_user'):
            flash("You must be a roaster to edit prices.", "danger")
            conn.close()
            return redirect(url_for('home')) # Or appropriate redirect

        auth_roaster_id = session.get('roaster_id')
        if not auth_roaster_id:
            flash("Roaster ID not found in your session. Cannot edit prices.", "danger")
            conn.close()
            return redirect(url_for('home')) # Or appropriate redirect

        bean_ids = request.form.getlist('bean_ids[]')
        prices = request.form.getlist('prices[]')

        updated_count = 0
        error_occurred = False
        try:
            # Update each bean's price, ensuring it belongs to the authenticated roaster
            for i in range(len(bean_ids)):
                bean_id = bean_ids[i]
                price = prices[i] # Consider validating price (e.g., is numeric, non-negative)

                # It's safer to also ensure selected_roaster_id (if used for display) matches auth_roaster_id
                # or that the beans being edited indeed belong to auth_roaster_id
                if selected_roaster_id and str(selected_roaster_id) != str(auth_roaster_id):
                    flash("Mismatch in roaster context. Operation aborted for safety.", "danger")
                    error_occurred = True
                    break # Stop processing further beans

                cursor.execute("""
                    UPDATE beans
                    SET price = ?
                    WHERE bean_id = ? AND roaster_id = ?
                """, (price, bean_id, auth_roaster_id))
                updated_count += cursor.rowcount

            if not error_occurred:
                conn.commit()
                if updated_count > 0:
                    flash(f'{updated_count} מחירים עודכנו בהצלחה', 'success')
                else:
                    flash('לא עודכנו מחירים. ייתכן שהפולים אינם שייכים לך או שלא נמצאו.', 'warning')

        except sqlite3.Error as e:
            conn.rollback()
            app.logger.error(f"Database error updating prices for roaster {auth_roaster_id}: {e}")
            flash("An error occurred while updating prices.", "danger")
        except Exception as e: # Catch other potential errors like ValueError from price conversion if added
            conn.rollback()
            app.logger.error(f"Unexpected error updating prices for roaster {auth_roaster_id}: {e}")
            flash("An unexpected error occurred.", "danger")

        # Redirect to the same page to refresh the data
        # Ensure selected_roaster_id for redirect is the one the user was viewing,
        # but the update operation was secured by auth_roaster_id.
        return redirect(url_for('edit_price', roaster_id=selected_roaster_id if selected_roaster_id else auth_roaster_id))

    conn.close()

    return render_template(
        'edit_price.html',
        roasters=roasters,
        beans=beans,
        selected_roaster_id=selected_roaster_id,
        selected_roaster_name=selected_roaster_name
    )

@app.context_processor
def inject_csrf_token():
    from flask_wtf.csrf import generate_csrf
    return dict(csrf_token=generate_csrf)

if __name__ == '__main__':
    # Remove debug=True before production
    app.run(host='0.0.0.0', port=5000)