<!DOCTYPE html>
<html lang="he" dir="rtl">
 <head>
  <meta charset="UTF-8">
   <title>Edit Bean</title>
   <style>
        body {font-family: sans-serif; margin: 20px; direction: rtl; text-align: right;}
         form label { display: block; margin-bottom: 5px; }
         form input, form textarea, select { margin-bottom: 10px; padding: 5px; direction: rtl;}
        
   
         .origin-container {
             margin-bottom: 15px;
         }
         .selected-countries {
             margin-bottom: 5px;
         }
         .country-tag {
             display: inline-block;
             background: #f0f0f0;
             padding: 3px 8px;
             margin: 2px;
             border-radius: 3px;
         }
         .remove-country {
             color: red;
             margin-right: 5px;
             cursor: pointer;
         }
         .modal {
             display: none;
             position: fixed;
             top: 0;
             left: 0;
             width: 100%;
             height: 100%;
             background: rgba(0,0,0,0.5);
         }
         .modal-content {
             background: white;
             width: 300px;
             padding: 20px;
             margin: 100px auto;
             border-radius: 5px;
         }
         /* Add styles for flavors container */
         .flavors-container {
             margin-bottom: 15px;
         }
         .selected-flavors {
             margin-bottom: 5px;
         }
         .flavor-tag {
             display: inline-block;
             background: #f0f0f0;
             padding: 3px 8px;
             margin: 2px;
             border-radius: 3px;
         }
         .remove-flavor {
             color: red;
             margin-right: 5px;
             cursor: pointer;
         }
         /* Add styles for roast level container */
         .roast-level-container {
             margin-bottom: 15px;
         }
         .selected-roast-levels {
             margin-bottom: 5px;
         }
         .roast-level-tag {
             display: inline-block;
             background: #f0f0f0;
             padding: 3px 8px;
             margin: 2px;
             border-radius: 3px;
         }
         .remove-roast-level {
             color: red;
             margin-right: 5px;
             cursor: pointer;
         }
         /* Three-column layout styles */
         .three-col {
             display: flex;
             gap: 20px;
         }
         .column {
             flex: 1;
             min-width: 0;
         }
         .button-style {
             padding: 10px 20px;
             font-size: 16px;
             cursor: pointer;
             border: 1px solid #ccc;
             border-radius: 5px;
             background-color: #f0f0f0;
             margin-left: 10px; /* Add some space between buttons */
             width: 120px; /* Fixed width to ensure same size */
             height: 40px; /* Fixed height to ensure same size */
             box-sizing: border-box; /* Include padding and border in the element's total width and height */
             display: inline-block; /* Ensure consistent display */
             text-align: center; /* Center text */
             vertical-align: top; /* Align buttons at the same level */
         }
         input[type="submit"].button-style {
             background-color: #4CAF50; /* Green for save button */
             color: white;
             border-color: #4CAF50;
         }
         button#cancel-button.button-style {
             background-color: #f44336; /* Red for cancel button */
             color: white;
             border-color: #f44336;
         }
   </style>
 </head>
  <body>
    <h1>עריכת פול קפה</h1>
    <form method="post">
        <input type="hidden" name="csrf_token" value="{{ csrf_token() }}">
        <input type="hidden" name="bean_id" value="{{ bean.bean_id }}">
        <div class="three-col">
            <div class="column">
                <!-- First Column Fields -->
                <label for="bean_name">שם פול:</label>
                <input type="text" name="bean_name" value="{{ bean.bean_name }}" required style="direction: rtl; width: 80%;"><br>

                <label for="origin">מקור:</label>
                <div class="origin-container">
                    <input type="hidden" name="origin" id="origin-input" value="{{ bean.origin }}">
                    <div id="selected-countries" class="selected-countries"></div>
                    <div class="country-controls">
                        <select id="country-selector" style="direction: rtl;">
                            <option value="">בחר מדינה</option>
                            {% for country in countries %}
                            <option value="{{ country.country }}">{{ country.country }}</option>
                            {% endfor %}
                        </select>
                        <!-- <button type="button" id="add-new-country">+</button> -->
                    </div>
                </div>

                <label for="processing">עיבוד:</label>
                <select name="processing" style="direction: rtl;">
                    <option value="">בחר עיבוד</option>
                    <option value="שטוף" {% if bean.processing == 'שטוף' %}selected{% endif %}>שטוף</option>
                    <option value="נטורל (יבש)" {% if bean.processing == 'נטורל (יבש)' %}selected{% endif %}>נטורל (יבש)</option>
                    <option value="הוני פרוסס" {% if bean.processing == 'הוני פרוסס' %}selected{% endif %}>הוני פרוסס</option>
                    <option value="אחר" {% if bean.processing == 'אחר' %}selected{% endif %}>אחר</option>
                </select><br>

                <label for="elevation">גובה:</label>
                <input type="text" name="elevation" value="{{ bean.elevation }}" style="direction: rtl;"><br>

                <label for="flavors">טעמים:</label>
                <div class="flavors-container">
                    <input type="hidden" name="flavors" id="flavors-input" value="{{ bean.flavors }}">
                    <div id="selected-flavors" class="selected-flavors"></div>
                    <div class="flavor-controls">
                        <select id="flavor-selector" style="direction: rtl;">
                            <option value="">בחר טעם</option>
                            {% for flavor in flavors %}
                            <option value="{{ flavor.c_flavor }}">{{ flavor.c_flavor }}</option>
                            {% endfor %}
                        </select>
                        <button type="button" id="clear-flavors">נקה הכל</button>
                    </div>
                </div>

                <label for="acidity">חמיצות:</label>
                <input type="text" name="acidity" value="{{ bean.acidity }}" style="direction: rtl;"><br>

                <label for="after_taste">שארית טעם:</label>
                <input type="text" name="after_taste" value="{{ bean.after_taste }}" style="direction: rtl;"><br>

                <label for="roast_level">דרגת קלייה:</label>
                <div class="roast-level-container">
                    <input type="hidden" name="roast_level" id="roast-level-input" value="{{ bean.roast_level }}">
                    <div id="selected-roast-levels" class="selected-roast-levels"></div>
                    <div class="roast-level-controls">
                        <select id="roast-level-selector" style="direction: rtl;">
                            <option value="">בחר דרגת קלייה</option>
                            <option value="בהירה">בהירה</option>
                            <option value="בהירה-בינונית">בהירה-בינונית</option>
                            <option value="בינונית">בינונית</option>
                            <option value="בינונית-כהה">בינונית-כהה</option>
                            <option value="כהה">כהה</option>
                        </select>
                        <button type="button" id="clear-roast-level">נקה</button>
                    </div>
                </div>
            </div>

            <div class="column">
                <!-- Second Column Fields -->
                <label for="body">גוף:</label>
                <select name="body" style="direction: rtl;">
                    <option value="" {% if bean.body == '' %}selected{% endif %}></option>
                    <option value="קל" {% if bean.body == 'קל' %}selected{% endif %}>קל</option>
                    <option value="בינוני" {% if bean.body == 'בינוני' %}selected{% endif %}>בינוני</option>
                    <option value="מלא" {% if bean.body == 'מלא' %}selected{% endif %}>מלא</option>
                </select><br>

                <label for="arabica">ערביקה(%):</label>
                <input type="number" name="arabica" value="{{ bean.arabica }}" style="direction: rtl;"><br>

                <label for="robusta">רובוסטה(%):</label>
                <input type="number" name="robusta" value="{{ bean.robusta }}" style="direction: rtl;"><br>

                <label for="mix">תערובת:</label>
                <select name="mix" required style="direction: rtl;">
                    <option value=True {% if bean.mix == True %} selected {% endif %}>כן</option>
                    <option value=False {% if bean.mix == False %} selected {% endif %}>לא</option>
                </select><br>

                <label for="price">מחיר:</label>
                <input type="number" name="price" step="0.01" value="{{ bean.price }}" style="direction: rtl;"><br>

                <label for="weight">משקל(ג):</label>
                <input type="number" name="weight" value="{{ bean.weight }}" style="direction: rtl;"><br>

                <label for="image_file">שם קובץ תמונה:</label>
                <input type="text" name="image_file" value="{{ bean.image_file }}" style="direction: rtl;"><br>

                <label for="SCA_score">SCA Score:</label>
                <input type="number" name="SCA_score" step="0.01" value="{{ bean.SCA_score }}" style="direction: rtl;"><br>

                <label for="Speciality">Specialty:</label>
                <select name="Speciality" required style="direction: rtl;">
                    <option value=True {% if bean.Speciality == True %} selected {% endif %}>כן</option>
                    <option value=False {% if bean.Speciality == False %} selected {% endif %}>לא</option>
                </select><br>

                <label for="decaf">נטול קפאין:</label>
                <select name="decaf" required style="direction: rtl;">
                    <option value=True {% if bean.decaf == True %} selected {% endif %}>כן</option>
                    <option value=False {% if bean.decaf == False %} selected {% endif %}>לא</option>
                </select><br>
            </div>

            <div class="column">
                <!-- Third Column Fields -->
                <label>שיטות הכנה</label>
                <br>
                <label for="turkish">טורקי:</label>
                <input type="checkbox" name="turkish" value="True" {% if bean.turkish == True %} checked {% endif %}>
                <br>
                <label for="espresso">אספרסו:</label>
                <input type="checkbox" name="espresso" value="True" {% if bean.espresso == True %} checked {% endif %}>
                <br>
                <label for="french_press">פרנץ פרס:</label>
                <input type="checkbox" name="french_press" value="True" {% if bean.french_press == True %} checked {% endif %}>
                <br>
                <label for="pour_over">מזיגה:</label>
                <input type="checkbox" name="pour_over" value="True" {% if bean.pour_over == True %} checked {% endif %}>
                <br>
                <label for="drip">טפטוף:</label>
                <input type="checkbox" name="drip" value="True" {% if bean.drip == True %} checked {% endif %}>
                <br>
                <label for="cold_brew">קולד ברו:</label>
                <input type="checkbox" name="cold_brew" value="True" {% if bean.cold_brew == True %} checked {% endif %}>
                <br><br>
                <input type="hidden" name="roaster_id" value="{{ bean.roaster_id }}">
                <div style="margin-top: 20px;">
                    <input type="submit" value="שמור שינויים" class="button-style">
                    <button type="button" id="cancel-button" class="button-style">ביטול</button>
                </div>
            </div>
        </div>
    </form>
    
    <!-- Add country modal -->
    <div id="country-modal" class="modal">
        <div class="modal-content">
            <span id="close-country" class="close-btn">&times;</span>
            <h3>הוסף מדינה חדשה</h3>
            <input type="text" id="new-country-input" placeholder="שם המדינה">
            <button id="save-country">שמור</button>
            <button id="cancel-country">בטל</button>
        </div>
    </div>

    <!-- Add flavor modal -->
    <div id="flavor-modal" class="modal">
        <div class="modal-content">
            <span id="close-flavor" class="close-btn">&times;</span>
            <h3>הוסף טעם חדש</h3>
            <input type="text" id="new-flavor-input" placeholder="שם הטעם">
            <button id="save-flavor">שמור</button>
            <button id="cancel-flavor">בטל</button>
        </div>
    </div>

  <script>
    // Initialize with existing values
    let selectedCountries = [];
    let selectedFlavors = [];
    let selectedRoastLevels = [];

    // Parse existing values to populate arrays
    document.addEventListener('DOMContentLoaded', function() {
        // Initialize countries
        const originValue = "{{ bean.origin }}";
        if (originValue) {
            // Split by comma and trim each value
            selectedCountries = originValue.split(',').map(country => country.trim());
            updateDisplay('country');
        }

        // Initialize flavors
        const flavorsValue = "{{ bean.flavors }}";
        if (flavorsValue) {
            selectedFlavors = flavorsValue.split(',').map(flavor => flavor.trim());
            updateDisplay('flavor');
        }

        // Initialize roast levels
        const roastLevelValue = "{{ bean.roast_level }}";
        if (roastLevelValue) {
            selectedRoastLevels = [roastLevelValue.trim()];
            updateDisplay('roast-level');
            // Set the selected option in the dropdown
            const roastLevelSelector = document.getElementById('roast-level-selector');
            for (let i = 0; i < roastLevelSelector.options.length; i++) {
                if (roastLevelSelector.options[i].value === roastLevelValue.trim()) {
                    roastLevelSelector.selectedIndex = i;
                    break;
                }
            }
        }
    });
    
    document.getElementById('country-selector').addEventListener('change', function() {
        const country = this.value;
        if (country && !selectedCountries.includes(country)) {
            selectedCountries.push(country);
            updateDisplay('country');
        }
        this.value = '';
    });

    // document.getElementById('add-new-country').addEventListener('click', function() {
    // document.getElementById('country-modal').style.display = 'block';
// });

document.getElementById('save-country').addEventListener('click', function() {
    const newCountry = document.getElementById('new-country-input').value.trim();
    if (newCountry && !selectedCountries.includes(newCountry)) {
        selectedCountries.push(newCountry);
        updateDisplay('country');
        addNewCountryToDB(newCountry);
    }
    document.getElementById('country-modal').style.display = 'none';
    document.getElementById('new-country-input').value = '';
});

document.getElementById('cancel-country').addEventListener('click', function() {
    document.getElementById('country-modal').style.display = 'none';
    document.getElementById('new-country-input').value = '';
});

document.getElementById('flavor-selector').addEventListener('change', function() {
    const flavor = this.value;
    if (flavor && !selectedFlavors.includes(flavor)) {
        selectedFlavors.push(flavor);
        updateDisplay('flavor');
    }
    this.value = '';
});

// document.getElementById('add-new-flavor').addEventListener('click', function() {
//     document.getElementById('flavor-modal').style.display = 'block';
// });

document.getElementById('save-flavor').addEventListener('click', function() {
    const newFlavor = document.getElementById('new-flavor-input').value.trim();
    if (newFlavor && !selectedFlavors.includes(newFlavor)) {
        selectedFlavors.push(newFlavor);
        updateDisplay('flavor');
        addNewFlavorToDB(newFlavor);
    }
    document.getElementById('flavor-modal').style.display = 'none';
    document.getElementById('new-flavor-input').value = '';
});

document.getElementById('cancel-flavor').addEventListener('click', function() {
    document.getElementById('flavor-modal').style.display = 'none';
    document.getElementById('new-flavor-input').value = '';
});

document.getElementById('roast-level-selector').addEventListener('change', function() {
    const selectedOption = this.value;
    selectedRoastLevels = selectedOption ? [selectedOption] : [];
    updateDisplay('roast-level');
});

document.getElementById('clear-flavors').addEventListener('click', function() {
    selectedFlavors = [];
    updateDisplay('flavor');
});

document.getElementById('clear-roast-level').addEventListener('click', function() {
    selectedRoastLevels = [];
    updateDisplay('roast-level');
    document.getElementById('roast-level-selector').selectedIndex = 0; // Reset dropdown
});

function updateDisplay(type) {
    if (type === 'country') {
        const container = document.getElementById('selected-countries');
        container.innerHTML = '';
        selectedCountries.forEach((country, index) => {
            const div = document.createElement('span');
            div.className = 'country-tag';
            div.innerHTML = `${country} <span class="remove-country" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('origin-input').value = selectedCountries.join(', ');
    } else if (type === 'flavor') {
        const container = document.getElementById('selected-flavors');
        container.innerHTML = '';
        selectedFlavors.forEach((flavor, index) => {
            const div = document.createElement('span');
            div.className = 'flavor-tag';
            div.innerHTML = `${flavor} <span class="remove-flavor" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('flavors-input').value = selectedFlavors.join(', ');
    } else if (type === 'roast-level') {
        const container = document.getElementById('selected-roast-levels');
        container.innerHTML = '';
        selectedRoastLevels.forEach((roastLevel, index) => {
            const div = document.createElement('span');
            div.className = 'roast-level-tag';
            div.innerHTML = `${roastLevel} <span class="remove-roast-level" data-index="${index}">×</span>`;
            container.appendChild(div);
        });
        document.getElementById('roast-level-input').value = selectedRoastLevels.join(', ');
    }
}

document.getElementById('selected-countries').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-country')) {
        const index = parseInt(e.target.dataset.index);
        selectedCountries.splice(index, 1);
        updateDisplay('country');
    }
});

document.getElementById('selected-flavors').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-flavor')) {
        const index = parseInt(e.target.dataset.index);
        selectedFlavors.splice(index, 1);
        updateDisplay('flavor');
    }
});

document.getElementById('selected-roast-levels').addEventListener('click', function(e) {
    if (e.target.classList.contains('remove-roast-level')) {
        const index = parseInt(e.target.dataset.index);
        selectedRoastLevels.splice(index, 1);
        updateDisplay('roast-level');
    }
});

function addNewCountryToDB(country) {
    fetch('/add_country', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ country: country }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Country added successfully');
        } else {
            console.error('Error adding country');
        }
    });
}

function addNewFlavorToDB(flavor) {
    fetch('/add_flavor', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
        },
        body: JSON.stringify({ flavor: flavor }),
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('Flavor added successfully');
        } else {
            console.error('Error adding flavor');
        }
    });
}


</script>
<script>
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('cancel-button').addEventListener('click', function() {
            window.location.replace('{{ url_for("manage_roaster") }}');
        });
    });
</script>
    </body>
</html>